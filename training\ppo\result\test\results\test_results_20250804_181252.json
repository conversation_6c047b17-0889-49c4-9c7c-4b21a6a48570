{"test_info": {"timestamp": "2025-08-04T18:12:52.501246", "model_path": "result\\train\\models\\seed_79811_2025_0804_172213\\best_model.pth", "total_episodes": 10, "test_duration": 8.679212808609009, "env_id": "parking-v0", "env_name": "custom_parking", "agent": "PPO_Clip", "seed": null}, "statistics": {"mean_score": -3.2245201155077665, "std_score": 0.758174312489003, "max_score": -1.4491740064695477, "min_score": -4.077824726700783, "median_score": -3.40452678874135, "success_rate": 0.0, "success_count": 0, "mean_episode_length": 27.6, "std_episode_length": 7.269112738154499, "max_episode_length": 42, "min_episode_length": 20}, "episode_details": [{"episode": 1, "score": -1.4491740064695477, "length": 31, "success": true, "info": {"speed": 5.218206257559355, "crashed": false, "action": [-0.20764003694057465, 0.49552121758461], "is_success": true, "path_distance": 0.9248151806760273, "path_index": 11, "path_following_reward": 0.1722555445797192, "action_change": 0.0, "smoothness_reward": -0.021462546110153184, "is_smooth_action": true, "reward_breakdown": {"base_reward": -0.10921702320947588, "path_reward": 0.1722555445797192, "smoothness_reward": -0.021462546110153184, "parking_reward": -0.6000000000000001, "total_reward": -0.11812010165048387}, "processed_action": [-0.20764003694057465, 0.49552121758461], "episode_step": 31, "episode_score": -1.44917399852797, "reset_obs": [0.0, 0.0, 0.0, 0.0, 0.48275649547576904, 0.8757546544075012, 0.05999999865889549, -0.14000000059604645, 0.0, 0.0, 6.123234262925839e-17, -1.0, 0.0, 0.0, 0.0, 0.0, 0.48275649547576904, 0.8757546544075012]}}, {"episode": 2, "score": -3.3771648555994034, "length": 24, "success": false, "info": {"speed": -5.363955220331748, "crashed": true, "action": [-0.08418045938014984, -0.04831007122993469], "is_success": false, "path_distance": 19.64684550649926, "path_index": 0, "path_following_reward": -0.1, "action_change": 0.0, "smoothness_reward": 0.008, "is_smooth_action": true, "reward_breakdown": {"base_reward": -5.342207839712522, "path_reward": -0.1, "smoothness_reward": 0.008, "parking_reward": 0.0, "total_reward": -1.3671519599281305}, "processed_action": [-0.08418045938014984, -0.04831007122993469], "episode_step": 24, "episode_score": -3.3771648393397666, "reset_obs": [0.0, 0.0, 0.0, 0.0, 0.7381090521812439, 0.6746814250946045, -0.10000000149011612, -0.14000000059604645, 0.0, 0.0, 6.123234262925839e-17, -1.0, 0.0, 0.0, 0.0, 0.0, 0.7381090521812439, 0.6746814250946045]}}, {"episode": 3, "score": -4.077824726700783, "length": 21, "success": false, "info": {"speed": -6.258146144449708, "crashed": true, "action": [-0.2794649302959442, 0.113775335252285], "is_success": false, "path_distance": 14.7758298641069, "path_index": 2, "path_following_reward": -0.1, "action_change": 0.0, "smoothness_reward": 0.046, "is_smooth_action": true, "reward_breakdown": {"base_reward": -5.497311358340412, "path_reward": -0.1, "smoothness_reward": 0.046, "parking_reward": 0.0, "total_reward": -1.3930351468075883}, "processed_action": [-0.2794649302959442, 0.113775335252285], "episode_step": 21, "episode_score": -4.077824683708115, "reset_obs": [0.0, 0.0, 0.0, 0.0, 0.7923483848571777, 0.6100688576698303, 0.10000000149011612, 0.14000000059604645, 0.0, 0.0, 6.123234262925839e-17, 1.0, 0.0, 0.0, 0.0, 0.0, 0.7923483848571777, 0.6100688576698303]}}, {"episode": 4, "score": -3.8703147135674953, "length": 21, "success": false, "info": {"speed": -5.254067711035411, "crashed": true, "action": [0.05717155337333679, 0.26530128717422485], "is_success": false, "path_distance": 10.783625070190562, "path_index": 8, "path_following_reward": -0.1, "action_change": 0.0, "smoothness_reward": -0.01583615446090698, "is_smooth_action": true, "reward_breakdown": {"base_reward": -5.445653019676684, "path_reward": -0.1, "smoothness_reward": -0.01583615446090698, "parking_reward": 0.0, "total_reward": -1.3953804858113525}, "processed_action": [0.05717155337333679, 0.26530128717422485], "episode_step": 21, "episode_score": -3.8703147196009136, "reset_obs": [0.0, 0.0, -0.0, -0.0, -0.002328932285308838, -0.9999973177909851, 0.05999999865889549, 0.14000000059604645, 0.0, 0.0, 6.123234262925839e-17, 1.0, 0.0, 0.0, -0.0, -0.0, -0.002328932285308838, -0.9999973177909851]}}, {"episode": 5, "score": -3.903481274843216, "length": 28, "success": false, "info": {"speed": -5.082153935896023, "crashed": true, "action": [-0.10086485743522644, -0.0037284959107637405], "is_success": false, "path_distance": 17.98925078631396, "path_index": 0, "path_following_reward": -0.1, "action_change": 0.0, "smoothness_reward": 0.058, "is_smooth_action": true, "reward_breakdown": {"base_reward": -5.470175165149444, "path_reward": -0.1, "smoothness_reward": 0.058, "parking_reward": 0.0, "total_reward": -1.389143791287361}, "processed_action": [-0.10086485743522644, -0.0037284959107637405], "episode_step": 28, "episode_score": -3.903481256837403, "reset_obs": [0.0, 0.0, -0.0, -0.0, -0.9270315766334534, -0.3749833106994629, -0.18000000715255737, -0.14000000059604645, 0.0, 0.0, 6.123234262925839e-17, -1.0, 0.0, 0.0, -0.0, -0.0, -0.9270315766334534, -0.3749833106994629]}}, {"episode": 6, "score": -2.325359344948083, "length": 23, "success": false, "info": {"speed": -5.765287174163041, "crashed": true, "action": [0.21840062737464905, 0.27942511439323425], "is_success": false, "path_distance": 4.060100284174434, "path_index": 3, "path_following_reward": -0.020601002841744338, "action_change": 0.0, "smoothness_reward": 0.012468986272811892, "is_smooth_action": true, "reward_breakdown": {"base_reward": -5.247078207826721, "path_reward": -0.020601002841744338, "smoothness_reward": 0.012468986272811892, "parking_reward": -0.1, "total_reward": -1.343656055554641}, "processed_action": [0.21840062737464905, 0.27942511439323425], "episode_step": 23, "episode_score": -2.3253593373397528, "reset_obs": [0.0, 0.0, -0.0, 0.0, -0.9999785423278809, 0.006547367665916681, 0.05999999865889549, -0.14000000059604645, 0.0, 0.0, 6.123234262925839e-17, -1.0, 0.0, 0.0, -0.0, 0.0, -0.9999785423278809, 0.006547367665916681]}}, {"episode": 7, "score": -3.431888721883297, "length": 20, "success": false, "info": {"speed": -5.688413416428698, "crashed": true, "action": [0.07292238622903824, 0.03597898781299591], "is_success": false, "path_distance": 8.507085851185323, "path_index": 8, "path_following_reward": -0.1, "action_change": 0.0, "smoothness_reward": 0.008, "is_smooth_action": true, "reward_breakdown": {"base_reward": -5.3734021595915324, "path_reward": -0.1, "smoothness_reward": 0.008, "parking_reward": 0.0, "total_reward": -1.3725505398978832}, "processed_action": [0.07292238622903824, 0.03597898781299591], "episode_step": 20, "episode_score": -3.431888773981496, "reset_obs": [0.0, 0.0, 0.0, 0.0, 0.6872371435165405, 0.7264331579208374, 0.05999999865889549, -0.14000000059604645, 0.0, 0.0, 6.123234262925839e-17, -1.0, 0.0, 0.0, 0.0, 0.0, 0.6872371435165405, 0.7264331579208374]}}, {"episode": 8, "score": -3.5367524847388268, "length": 39, "success": false, "info": {"speed": 4.638589163310824, "crashed": true, "action": [-0.43684500455856323, -0.12030375748872757], "is_success": false, "path_distance": 5.0217179941669094, "path_index": 14, "path_following_reward": -0.030217179941669094, "action_change": 0.0, "smoothness_reward": 0.046, "is_smooth_action": true, "reward_breakdown": {"base_reward": -5.227624321927926, "path_reward": -0.030217179941669094, "smoothness_reward": 0.046, "parking_reward": 0.0, "total_reward": -1.307571234464482}, "processed_action": [-0.43684500455856323, -0.12030375748872757], "episode_step": 39, "episode_score": -3.53675242253121, "reset_obs": [0.0, 0.0, 0.0, 0.0, 0.5362212657928467, 0.844077467918396, -0.05999999865889549, -0.14000000059604645, 0.0, 0.0, 6.123234262925839e-17, -1.0, 0.0, 0.0, 0.0, 0.0, 0.5362212657928467, 0.844077467918396]}}, {"episode": 9, "score": -3.1010718382894993, "length": 42, "success": false, "info": {"speed": 2.1342837313811014, "crashed": true, "action": [-0.5032569766044617, 0.015384197235107422], "is_success": false, "path_distance": 4.610174544835643, "path_index": 8, "path_following_reward": -0.02610174544835643, "action_change": 0.0, "smoothness_reward": 0.026, "is_smooth_action": true, "reward_breakdown": {"base_reward": -5.18845521551728, "path_reward": -0.02610174544835643, "smoothness_reward": 0.026, "parking_reward": 0.0, "total_reward": -1.2997443275138267}, "processed_action": [-0.5032569766044617, 0.015384197235107422], "episode_step": 42, "episode_score": -3.101071800061659, "reset_obs": [0.0, 0.0, 0.0, 0.0, 0.9816275835037231, 0.1908068209886551, -0.2199999988079071, -0.14000000059604645, 0.0, 0.0, 6.123234262925839e-17, -1.0, 0.0, 0.0, 0.0, 0.0, 0.9816275835037231, 0.1908068209886551]}}, {"episode": 10, "score": -3.1721691880375147, "length": 27, "success": false, "info": {"speed": -5.370883443134805, "crashed": true, "action": [-0.24036234617233276, -0.19581346213817596], "is_success": false, "path_distance": 5.2556991842075504, "path_index": 10, "path_following_reward": -0.03255699184207551, "action_change": 0.0, "smoothness_reward": 0.046, "is_smooth_action": true, "reward_breakdown": {"base_reward": -5.300438650623424, "path_reward": -0.03255699184207551, "smoothness_reward": 0.046, "parking_reward": 0.0, "total_reward": -1.3288767602084788}, "processed_action": [-0.24036234617233276, -0.19581346213817596], "episode_step": 27, "episode_score": -3.172169218062504, "reset_obs": [0.0, 0.0, 0.0, 0.0, 0.5121647119522095, 0.858887255191803, -0.2199999988079071, -0.14000000059604645, 0.0, 0.0, 6.123234262925839e-17, -1.0, 0.0, 0.0, 0.0, 0.0, 0.5121647119522095, 0.858887255191803]}}], "config": {"max_episode_steps": 150, "render": true, "render_mode": "human", "device": "cuda:0"}}