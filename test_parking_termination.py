#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试新的停车终止机制：只有真正停车才算回合结束
"""

import warnings
import os
warnings.filterwarnings('ignore')
os.environ['GYMNASIUM_DISABLE_WARNINGS'] = 'True'

import numpy as np
from algorithms.environments.single_agent_env.custom_parking import CustomParkingEnv
from argparse import Namespace

def test_parking_termination():
    """测试停车终止机制"""
    print("🚗 测试新的停车终止机制")
    print("=" * 60)
    print("新终止条件:")
    print("1. 必须在目标区域内（1.5米内）")
    print("2. 必须基本停车（速度<0.5m/s）")
    print("3. 必须连续停车3步以上")
    print("4. 移除了延迟终止机制")
    print("-" * 60)
    
    # 创建环境
    config = Namespace()
    config.env_seed = 42
    config.max_episode_steps = 150
    config.collision_reward = -1.0
    config.render_mode = None
    config.debug = True  # 开启调试信息
    
    env = CustomParkingEnv(config)
    
    # 测试不同的停车场景
    test_scenarios = [
        {
            "name": "正常停车测试",
            "description": "模拟智能体正常接近并停车",
            "strategy": "normal_parking"
        },
        {
            "name": "快速通过测试", 
            "description": "模拟智能体快速通过目标点不停车",
            "strategy": "fast_pass"
        },
        {
            "name": "慢速通过测试",
            "description": "模拟智能体慢速通过目标点但不停车", 
            "strategy": "slow_pass"
        },
        {
            "name": "停车后继续测试",
            "description": "模拟智能体停车后又开始移动",
            "strategy": "stop_then_move"
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\n📊 {scenario['name']}")
        print(f"描述: {scenario['description']}")
        print("-" * 40)
        
        obs, info = env.reset()
        done = False
        step_count = 0
        episode_reward = 0
        
        # 记录关键状态
        min_distance = float('inf')
        min_speed = float('inf')
        parking_attempts = 0
        
        while not done and step_count < 150:
            vehicle = env.controlled_vehicles[0]
            goal_distance = np.linalg.norm(vehicle.position - vehicle.goal.position)
            current_speed = abs(vehicle.speed)
            
            min_distance = min(min_distance, goal_distance)
            min_speed = min(min_speed, current_speed)
            
            # 根据测试策略选择动作
            action = get_test_action(scenario['strategy'], goal_distance, current_speed, step_count)
            
            # 执行动作
            obs, reward, terminated, truncated, info = env.step(action)
            done = terminated or truncated
            
            episode_reward += reward
            step_count += 1
            
            # 记录停车尝试
            if goal_distance < 1.5 and current_speed < 0.5:
                parking_attempts += 1
            
            # 显示关键信息
            if step_count % 20 == 0 or done:
                parking_steps = getattr(env, 'parking_detection_steps', 0)
                print(f"  步骤 {step_count}: 距离={goal_distance:.2f}m, 速度={current_speed:.2f}m/s, "
                      f"停车检测={parking_steps}/3")
        
        # 分析结果
        final_distance = np.linalg.norm(vehicle.position - vehicle.goal.position)
        final_speed = abs(vehicle.speed)
        final_parking_steps = getattr(env, 'parking_detection_steps', 0)
        
        print(f"\n  结果分析:")
        print(f"    总步数: {step_count}")
        print(f"    最终距离: {final_distance:.2f}m")
        print(f"    最终速度: {final_speed:.2f}m/s")
        print(f"    最近距离: {min_distance:.2f}m")
        print(f"    最低速度: {min_speed:.2f}m/s")
        print(f"    停车尝试次数: {parking_attempts}")
        print(f"    最终停车检测: {final_parking_steps}/3")
        print(f"    总奖励: {episode_reward:.2f}")
        
        # 判断终止原因
        if terminated:
            if final_parking_steps >= 3:
                print(f"    🎯 终止原因: 成功停车 ✅")
            else:
                print(f"    ❌ 终止原因: 其他原因（碰撞/出界等）")
        elif truncated:
            print(f"    ⏰ 终止原因: 超时")
        
        # 评估测试结果
        evaluate_scenario_result(scenario, final_distance, final_speed, final_parking_steps, terminated)
    
    env.close()
    
    print(f"\n💡 新停车终止机制总结:")
    print("✅ 优点:")
    print("  1. 明确的成功条件：必须真正停车")
    print("  2. 避免误判：连续3步停车才算成功")
    print("  3. 自然终止：不依赖延迟机制")
    print("  4. 鼓励学习：智能体必须学会主动停车")

def get_test_action(strategy, goal_distance, current_speed, step_count):
    """根据测试策略生成动作"""
    if strategy == "normal_parking":
        # 正常停车：远距离加速，近距离减速，目标点停车
        if goal_distance > 5.0:
            return np.array([0.6, 0.0])  # 加速接近
        elif goal_distance > 2.0:
            return np.array([0.2, 0.0])  # 减速接近
        elif goal_distance > 1.0:
            return np.array([-0.3, 0.0]) if current_speed > 1.0 else np.array([0.1, 0.0])
        else:
            # 在目标点附近，尝试停车
            if current_speed > 0.5:
                return np.array([-0.5, 0.0])  # 刹车
            else:
                return np.array([0.0, 0.0])   # 保持停车
                
    elif strategy == "fast_pass":
        # 快速通过：始终保持较高速度
        return np.array([0.8, 0.0])
        
    elif strategy == "slow_pass":
        # 慢速通过：保持低速但不停车
        if goal_distance < 1.5:
            return np.array([0.3, 0.0])  # 在目标点附近仍然前进
        else:
            return np.array([0.4, 0.0])
            
    elif strategy == "stop_then_move":
        # 停车后继续：先停车再移动
        if goal_distance > 2.0:
            return np.array([0.5, 0.0])  # 接近
        elif step_count < 100:
            # 前期尝试停车
            if current_speed > 0.3:
                return np.array([-0.4, 0.0])
            else:
                return np.array([0.0, 0.0])
        else:
            # 后期重新移动
            return np.array([0.4, 0.0])
    
    return np.array([0.0, 0.0])

def evaluate_scenario_result(scenario, final_distance, final_speed, parking_steps, terminated):
    """评估测试场景结果"""
    strategy = scenario['strategy']
    
    if strategy == "normal_parking":
        if terminated and parking_steps >= 3:
            print(f"    ✅ 测试通过: 正确识别停车成功")
        else:
            print(f"    ❌ 测试失败: 未能正确停车")
            
    elif strategy == "fast_pass":
        if not terminated or parking_steps < 3:
            print(f"    ✅ 测试通过: 正确拒绝快速通过")
        else:
            print(f"    ❌ 测试失败: 错误识别为停车")
            
    elif strategy == "slow_pass":
        if not terminated or parking_steps < 3:
            print(f"    ✅ 测试通过: 正确拒绝慢速通过")
        else:
            print(f"    ❌ 测试失败: 错误识别为停车")
            
    elif strategy == "stop_then_move":
        # 这个测试比较复杂，主要看是否能检测到重新移动
        if parking_steps < 3:
            print(f"    ✅ 测试通过: 正确检测到重新移动")
        else:
            print(f"    ⚠️ 测试结果: 可能在移动前就终止了")

def main():
    """主函数"""
    print("🚀 停车终止机制测试")
    print("=" * 60)
    
    test_parking_termination()
    
    print("\n✅ 测试完成！")
    print("\n🎯 关键改进:")
    print("1. 🎯 明确成功条件：真正停车才算成功")
    print("2. 🔄 连续检测：避免瞬间停车的误判")
    print("3. 🚫 移除延迟：不再依赖人工延迟")
    print("4. 📚 自然学习：智能体必须学会主动停车")

if __name__ == "__main__":
    main()
