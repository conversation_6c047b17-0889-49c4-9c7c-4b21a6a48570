<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="d775df3f-d0d4-4671-8549-3782e03347c2" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="308jVUM4TrFJ1qU2NAM9ydMVlve" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;Python.check_files.executor&quot;: &quot;Run&quot;,
    &quot;Python.check_model.executor&quot;: &quot;Run&quot;,
    &quot;Python.custom_parking.executor&quot;: &quot;Run&quot;,
    &quot;Python.debug_env.executor&quot;: &quot;Run&quot;,
    &quot;Python.demo_custom_parking.executor&quot;: &quot;Run&quot;,
    &quot;Python.ppo_parking.executor&quot;: &quot;Run&quot;,
    &quot;Python.ppoclip_agent.executor&quot;: &quot;Run&quot;,
    &quot;Python.simple_parking_demo.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_best_model.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_custom_parking_basic.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_custom_parking_simple.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_debug_hybrid_astar.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_episode_duration.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_fixed_camera.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_minimal_parking.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_parking_model.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_ppo_parking.executor&quot;: &quot;Run&quot;,
    &quot;Python.test_wider_parking.executor&quot;: &quot;Run&quot;,
    &quot;Python.train_ppo_parking.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/PycharmProject/HRL/training/ppo/ppo_configs&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\PycharmProject\HRL\training\ppo\ppo_configs" />
    </key>
  </component>
  <component name="RunManager" selected="Python.test_ppo_parking">
    <configuration name="test_ppo_parking" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="xuance" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/training/ppo" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/training/ppo/test_ppo_parking.py" />
      <option name="PARAMETERS" value="--render 1" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="train_ppo_parking" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="xuance" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/training/ppo" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/training/ppo/train_ppo_parking.py" />
      <option name="PARAMETERS" value="--benchmark 1" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Python.test_ppo_parking" />
      <item itemvalue="Python.train_ppo_parking" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Python.test_ppo_parking" />
        <item itemvalue="Python.train_ppo_parking" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-PY-251.25410.159" />
        <option value="bundled-python-sdk-e0ed3721d81e-36ea0e71a18c-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.25410.159" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="d775df3f-d0d4-4671-8549-3782e03347c2" name="Changes" comment="" />
      <created>1753016117346</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753016117346</updated>
      <workItem from="1753016118438" duration="1023000" />
      <workItem from="1753024585284" duration="25000" />
      <workItem from="1753024884070" duration="157000" />
      <workItem from="1753025060301" duration="1081000" />
      <workItem from="1753026769722" duration="4006000" />
      <workItem from="1753062486434" duration="9268000" />
      <workItem from="1753073999169" duration="1965000" />
      <workItem from="1753075985833" duration="8575000" />
      <workItem from="1753106922355" duration="1508000" />
      <workItem from="1753159197819" duration="17832000" />
      <workItem from="1753262097875" duration="3000" />
      <workItem from="1753370424613" duration="354000" />
      <workItem from="1753406950553" duration="928000" />
      <workItem from="1753407903775" duration="445000" />
      <workItem from="1753409559890" duration="1993000" />
      <workItem from="1753411576369" duration="2698000" />
      <workItem from="1753415473309" duration="470000" />
      <workItem from="1753415952976" duration="5244000" />
      <workItem from="1753422502416" duration="14000" />
      <workItem from="1753422530222" duration="4815000" />
      <workItem from="1753504226077" duration="2121000" />
      <workItem from="1753750785533" duration="2022000" />
      <workItem from="1753760202755" duration="6434000" />
      <workItem from="1753864087484" duration="9684000" />
      <workItem from="1753924047147" duration="13769000" />
      <workItem from="1753946277654" duration="302000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/algorithms/agents/agents/policy_gradient/ppoclip_agent.py</url>
          <line>222</line>
          <option name="timeStamp" value="13" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/HRL$demo_custom_parking.coverage" NAME="demo_custom_parking Coverage Results" MODIFIED="1753364237275" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/HRL$test_fixed_camera.coverage" NAME="test_fixed_camera Coverage Results" MODIFIED="1753411125844" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/HRL$check_files.coverage" NAME="check_files Coverage Results" MODIFIED="1753423494517" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/HRL$check_model.coverage" NAME="check_model Coverage Results" MODIFIED="1753082264144" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/training/ppo" />
    <SUITE FILE_PATH="coverage/HRL$test_wider_parking.coverage" NAME="test_wider_parking Coverage Results" MODIFIED="1753412952059" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/HRL$test_custom_parking_basic.coverage" NAME="test_custom_parking_basic Coverage Results" MODIFIED="1753364159797" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/HRL$train_ppo_parking.coverage" NAME="train_ppo_parking Coverage Results" MODIFIED="1754299329292" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/training/ppo" />
    <SUITE FILE_PATH="coverage/HRL$ppo_parking.coverage" NAME="ppo_parking Coverage Results" MODIFIED="1753407821345" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/training/ppo" />
    <SUITE FILE_PATH="coverage/HRL$ppoclip_agent.coverage" NAME="ppoclip_agent Coverage Results" MODIFIED="1753175844261" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/algorithms/agents/agents/policy_gradient" />
    <SUITE FILE_PATH="coverage/HRL$debug_visual_test.coverage" NAME="debug_visual_test Coverage Results" MODIFIED="1753335505724" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/HRL$test_best_model.coverage" NAME="test_best_model Coverage Results" MODIFIED="1753363477290" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/training/ppo" />
    <SUITE FILE_PATH="coverage/HRL$debug_env.coverage" NAME="debug_env Coverage Results" MODIFIED="1753370155427" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/training/ppo" />
    <SUITE FILE_PATH="coverage/HRL$test_custom_parking.coverage" NAME="test_custom_parking Coverage Results" MODIFIED="1753335211990" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/HRL$test_original_parking.coverage" NAME="test_original_parking Coverage Results" MODIFIED="1753335651677" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/HRL$test_custom_parking_simple.coverage" NAME="test_custom_parking_simple Coverage Results" MODIFIED="1753410651263" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/HRL$test_episode_duration.coverage" NAME="test_episode_duration Coverage Results" MODIFIED="1753415479711" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/HRL$test_ppo_parking.coverage" NAME="test_ppo_parking Coverage Results" MODIFIED="1754303399859" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/training/ppo" />
    <SUITE FILE_PATH="coverage/HRL$test_debug_hybrid_astar.coverage" NAME="test_debug_hybrid_astar Coverage Results" MODIFIED="1753925513974" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/HRL$visual_test_parking.coverage" NAME="visual_test_parking Coverage Results" MODIFIED="1753335359682" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/HRL$test_minimal_parking.coverage" NAME="test_minimal_parking Coverage Results" MODIFIED="1753364675797" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/HRL$test_parking_model.coverage" NAME="test_parking_model Coverage Results" MODIFIED="1753080280413" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/training/ppo" />
    <SUITE FILE_PATH="coverage/xuance$ppo_parking.coverage" NAME="ppo_parking Coverage Results" MODIFIED="1753026710123" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/Train/ppo" />
    <SUITE FILE_PATH="coverage/HRL$debug_test.coverage" NAME="debug_test Coverage Results" MODIFIED="1753324815722" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/training/ppo" />
    <SUITE FILE_PATH="coverage/HRL$simple_parking_demo.coverage" NAME="simple_parking_demo Coverage Results" MODIFIED="1753411950346" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/HRL$custom_parking.coverage" NAME="custom_parking Coverage Results" MODIFIED="1753411311743" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/algorithms/environments/single_agent_env" />
  </component>
</project>