#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试调试控制功能：验证enable_debug_info参数的作用
"""

import warnings
import os
warnings.filterwarnings('ignore')
os.environ['GYMNASIUM_DISABLE_WARNINGS'] = 'True'

import numpy as np
from algorithms.environments.single_agent_env.custom_parking import CustomParkingEnv
from argparse import Namespace
import time

def test_debug_control():
    """测试调试控制功能"""
    print("🔧 测试调试控制功能")
    print("=" * 60)
    
    # 测试场景1：关闭调试信息（默认）
    print("\n📊 测试场景1: 关闭调试信息（默认设置）")
    print("-" * 40)
    
    config1 = Namespace()
    config1.env_seed = 42
    config1.max_episode_steps = 50
    config1.collision_reward = -1.0
    config1.render_mode = None
    # 注意：没有设置enable_debug_info，应该默认为False
    
    env1 = CustomParkingEnv(config1)
    print(f"调试信息状态: {env1.enable_debug_info}")
    
    start_time = time.time()
    obs, info = env1.reset()
    
    # 快速运行几步，测试是否有调试输出
    for step in range(10):
        action = np.array([0.5, 0.0])  # 简单前进
        obs, reward, terminated, truncated, info = env1.step(action)
        if terminated or truncated:
            break
    
    end_time = time.time()
    elapsed_time = end_time - start_time
    print(f"运行时间: {elapsed_time:.2f}秒 (应该很快，没有延迟)")
    env1.close()
    
    # 测试场景2：开启调试信息
    print("\n📊 测试场景2: 开启调试信息")
    print("-" * 40)
    
    config2 = Namespace()
    config2.env_seed = 42
    config2.max_episode_steps = 50
    config2.collision_reward = -1.0
    config2.render_mode = None
    config2.enable_debug_info = True  # 明确开启调试信息
    
    env2 = CustomParkingEnv(config2)
    print(f"调试信息状态: {env2.enable_debug_info}")
    
    start_time = time.time()
    obs, info = env2.reset()
    
    # 运行到终止，测试调试输出和延迟
    step_count = 0
    while step_count < 50:
        vehicle = env2.controlled_vehicles[0]
        goal_distance = np.linalg.norm(vehicle.position - vehicle.goal.position)
        
        # 模拟停车行为
        if goal_distance > 3.0:
            action = np.array([0.6, 0.0])  # 接近
        elif goal_distance > 1.5:
            action = np.array([0.3, 0.0])  # 减速
        else:
            # 尝试停车
            if abs(vehicle.speed) > 0.5:
                action = np.array([-0.4, 0.0])  # 刹车
            else:
                action = np.array([0.0, 0.0])   # 保持停车
        
        obs, reward, terminated, truncated, info = env2.step(action)
        step_count += 1
        
        if terminated or truncated:
            print(f"回合在第{step_count}步结束")
            break
    
    end_time = time.time()
    elapsed_time = end_time - start_time
    print(f"运行时间: {elapsed_time:.2f}秒 (如果成功停车，应该包含3秒延迟)")
    env2.close()
    
    # 测试场景3：显式关闭调试信息
    print("\n📊 测试场景3: 显式关闭调试信息")
    print("-" * 40)
    
    config3 = Namespace()
    config3.env_seed = 42
    config3.max_episode_steps = 50
    config3.collision_reward = -1.0
    config3.render_mode = None
    config3.enable_debug_info = False  # 显式关闭
    
    env3 = CustomParkingEnv(config3)
    print(f"调试信息状态: {env3.enable_debug_info}")
    
    start_time = time.time()
    obs, info = env3.reset()
    
    # 快速运行几步
    for step in range(10):
        action = np.array([0.5, 0.0])
        obs, reward, terminated, truncated, info = env3.step(action)
        if terminated or truncated:
            break
    
    end_time = time.time()
    elapsed_time = end_time - start_time
    print(f"运行时间: {elapsed_time:.2f}秒 (应该很快，没有延迟)")
    env3.close()
    
    print(f"\n💡 调试控制功能总结:")
    print("✅ 功能验证:")
    print("  1. 默认情况下，enable_debug_info = False")
    print("  2. 只有配置文件明确设置True时才启用调试")
    print("  3. 调试信息包括：停车检测、回合结果、3秒延迟")
    print("  4. 关闭调试时，运行速度更快，无额外输出")

def test_config_integration():
    """测试配置文件集成"""
    print("\n🔧 测试配置文件集成")
    print("=" * 60)
    
    # 模拟不同的配置文件设置
    configs = [
        {"name": "训练配置", "enable_debug_info": False, "description": "训练时关闭调试"},
        {"name": "测试配置", "enable_debug_info": True, "description": "测试时开启调试"},
        {"name": "默认配置", "description": "没有设置enable_debug_info"}
    ]
    
    for config_info in configs:
        print(f"\n📋 {config_info['name']}: {config_info['description']}")
        
        config = Namespace()
        config.env_seed = 42
        config.max_episode_steps = 30
        config.collision_reward = -1.0
        config.render_mode = None
        
        if "enable_debug_info" in config_info:
            config.enable_debug_info = config_info["enable_debug_info"]
        
        env = CustomParkingEnv(config)
        print(f"   实际调试状态: {env.enable_debug_info}")
        
        # 简单测试
        obs, info = env.reset()
        action = np.array([0.0, 0.0])
        obs, reward, terminated, truncated, info = env.step(action)
        
        env.close()

def main():
    """主函数"""
    print("🚀 调试控制功能测试")
    print("=" * 60)
    
    test_debug_control()
    test_config_integration()
    
    print("\n✅ 测试完成！")
    print("\n🎯 使用说明:")
    print("1. 🔧 训练时: 不设置enable_debug_info或设为False")
    print("2. 🔍 调试时: 在配置中设置enable_debug_info=True")
    print("3. 📊 调试功能: 包括详细输出和3秒延迟")
    print("4. ⚡ 性能: 关闭调试时运行更快")
    
    print("\n📝 配置文件示例:")
    print("# 训练配置 (train_config.yaml)")
    print("enable_debug_info: False  # 或者不设置此项")
    print("")
    print("# 测试配置 (test_config.yaml)")  
    print("enable_debug_info: True   # 开启详细调试信息")

if __name__ == "__main__":
    main()
