{"test_info": {"timestamp": "2025-08-04T18:14:40.334226", "model_path": "result\\train\\models\\seed_79811_2025_0804_172213\\best_model.pth", "total_episodes": 10, "test_duration": 38.291422843933105, "env_id": "parking-v0", "env_name": "custom_parking", "agent": "PPO_Clip", "seed": null}, "statistics": {"mean_score": -4.195127390616108, "std_score": 2.886884643799168, "max_score": -0.3479871656745672, "min_score": -12.083819530904293, "median_score": -3.434330327436328, "success_rate": 0.0, "success_count": 0, "mean_episode_length": 26.7, "std_episode_length": 6.957729514719583, "max_episode_length": 43, "min_episode_length": 19}, "episode_details": [{"episode": 1, "score": -4.390507832169533, "length": 30, "success": false, "info": {"speed": -5.848226685490873, "crashed": true, "action": [-0.1349627524614334, 0.19107122719287872], "is_success": false, "path_distance": 6.612023047080353, "path_index": 10, "path_following_reward": -0.04612023047080353, "action_change": 0.0, "smoothness_reward": 0.054000000000000006, "is_smooth_action": true, "reward_breakdown": {"base_reward": -5.347124933659958, "path_reward": -0.04612023047080353, "smoothness_reward": 0.054000000000000006, "parking_reward": 0.0, "collision_avoidance_reward": -3.561603422376253, "total_reward": -1.6094955577061305}, "processed_action": [-0.1349627524614334, 0.19107122719287872], "episode_step": 30, "episode_score": -4.390507885251308, "reset_obs": [0.0, 0.0, 0.0, 0.0, 0.8945118188858032, 0.447044312953949, 0.05999999865889549, -0.14000000059604645, 0.0, 0.0, 6.123234262925839e-17, -1.0, 0.0, 0.0, 0.0, 0.0, 0.8945118188858032, 0.447044312953949]}}, {"episode": 2, "score": -3.4790204912424088, "length": 20, "success": false, "info": {"speed": -6.028547507731449, "crashed": true, "action": [-0.1725677102804184, -0.3247925043106079], "is_success": false, "path_distance": 5.511129562713945, "path_index": 2, "path_following_reward": -0.03511129562713945, "action_change": 0.0, "smoothness_reward": 0.015024899482727054, "is_smooth_action": true, "reward_breakdown": {"base_reward": -5.308354800973696, "path_reward": -0.03511129562713945, "smoothness_reward": 0.015024899482727054, "parking_reward": 0.0, "collision_avoidance_reward": -3.5676425085266796, "total_reward": -1.615311247124215}, "processed_action": [-0.1725677102804184, -0.3247925043106079], "episode_step": 20, "episode_score": -3.4790204580279074, "reset_obs": [0.0, 0.0, -0.0, -0.0, -0.7275224924087524, -0.6860838532447815, 0.019999999552965164, -0.14000000059604645, 0.0, 0.0, 6.123234262925839e-17, -1.0, 0.0, 0.0, -0.0, -0.0, -0.7275224924087524, -0.6860838532447815]}}, {"episode": 3, "score": -4.179311823099852, "length": 30, "success": false, "info": {"speed": -5.239951908588414, "crashed": true, "action": [-0.12619474530220032, -0.11332292854785919], "is_success": false, "path_distance": 4.827571864895315, "path_index": 2, "path_following_reward": -0.028275718648953153, "action_change": 0.0, "smoothness_reward": 0.046, "is_smooth_action": true, "reward_breakdown": {"base_reward": -5.290740342006532, "path_reward": -0.028275718648953153, "smoothness_reward": 0.046, "parking_reward": -0.1, "collision_avoidance_reward": -3.5679021671452533, "total_reward": -1.6209023231353328}, "processed_action": [-0.12619474530220032, -0.11332292854785919], "episode_step": 30, "episode_score": -4.179311834964831, "reset_obs": [0.0, 0.0, -0.0, -0.0, -0.7914447784423828, -0.6112406849861145, -0.05999999865889549, -0.14000000059604645, 0.0, 0.0, 6.123234262925839e-17, -1.0, 0.0, 0.0, -0.0, -0.0, -0.7914447784423828, -0.6112406849861145]}}, {"episode": 4, "score": -2.9188339040847495, "length": 23, "success": false, "info": {"speed": 6.538641490538915, "crashed": true, "action": [0.07146649807691574, 0.42611488699913025], "is_success": false, "path_distance": 3.8678285954525253, "path_index": 4, "path_following_reward": 0.08396514213642425, "action_change": 0.0, "smoothness_reward": -0.04313378643989561, "is_smooth_action": true, "reward_breakdown": {"base_reward": -5.139165429527631, "path_reward": 0.08396514213642425, "smoothness_reward": -0.04313378643989561, "parking_reward": -0.1, "collision_avoidance_reward": -3.5704207180016163, "total_reward": -1.6032070505645926}, "processed_action": [0.07146649807691574, 0.42611488699913025], "episode_step": 23, "episode_score": -2.918833995530017, "reset_obs": [0.0, 0.0, -0.0, 0.0, -0.9326289892196655, 0.36083680391311646, 0.05999999865889549, 0.14000000059604645, 0.0, 0.0, 6.123234262925839e-17, 1.0, 0.0, 0.0, -0.0, 0.0, -0.9326289892196655, 0.36083680391311646]}}, {"episode": 5, "score": -3.389640163630247, "length": 20, "success": false, "info": {"speed": -6.004478756338356, "crashed": true, "action": [0.18397805094718933, 0.251943439245224], "is_success": false, "path_distance": 6.355220549119409, "path_index": 2, "path_following_reward": -0.04355220549119409, "action_change": 0.0, "smoothness_reward": 0.015766787290573122, "is_smooth_action": true, "reward_breakdown": {"base_reward": -5.326447095070126, "path_reward": -0.04355220549119409, "smoothness_reward": 0.015766787290573122, "parking_reward": 0.0, "collision_avoidance_reward": -3.56267744123533, "total_reward": -1.6108380062954548}, "processed_action": [0.18397805094718933, 0.251943439245224], "episode_step": 20, "episode_score": -3.389640102610789, "reset_obs": [0.0, 0.0, 0.0, -0.0, 0.841447114944458, -0.540339469909668, -0.019999999552965164, 0.14000000059604645, 0.0, 0.0, 6.123234262925839e-17, 1.0, 0.0, 0.0, 0.0, -0.0, 0.841447114944458, -0.540339469909668]}}, {"episode": 6, "score": -3.1194206587970257, "length": 32, "success": true, "info": {"speed": 4.723499067127706, "crashed": false, "action": [-0.3649514615535736, 0.5664159059524536], "is_success": true, "path_distance": 0.6347299852452634, "path_index": 10, "path_following_reward": 0.1809581004426421, "action_change": 0.0, "smoothness_reward": -0.029969908714294426, "is_smooth_action": true, "reward_breakdown": {"base_reward": -0.11177905686159977, "path_reward": 0.1809581004426421, "smoothness_reward": -0.029969908714294426, "parking_reward": 0.30000000000000004, "collision_avoidance_reward": -1.4559137507514857, "total_reward": -0.12739883518152642}, "processed_action": [-0.3649514615535736, 0.5664159059524536], "episode_step": 32, "episode_score": -3.1194206728809704, "reset_obs": [0.0, 0.0, -0.0, 0.0, -0.3474588990211487, 0.9376952052116394, -0.10000000149011612, -0.14000000059604645, 0.0, 0.0, 6.123234262925839e-17, -1.0, 0.0, 0.0, -0.0, 0.0, -0.3474588990211487, 0.9376952052116394]}}, {"episode": 7, "score": -2.9577451134100556, "length": 25, "success": false, "info": {"speed": 4.467720492945774, "crashed": true, "action": [0.14061355590820312, 0.47853630781173706], "is_success": false, "path_distance": 4.070692013077121, "path_index": 4, "path_following_reward": -0.02070692013077121, "action_change": 0.0, "smoothness_reward": -0.01942435693740844, "is_smooth_action": true, "reward_breakdown": {"base_reward": -5.1610142024229635, "path_reward": -0.02070692013077121, "smoothness_reward": -0.01942435693740844, "parking_reward": -0.1, "collision_avoidance_reward": -3.5797472867947038, "total_reward": -1.611362843474807}, "processed_action": [0.14061355590820312, 0.47853630781173706], "episode_step": 25, "episode_score": -2.9577451430627812, "reset_obs": [0.0, 0.0, 0.0, 0.0, 0.14384864270687103, 0.9895997047424316, -0.18000000715255737, -0.14000000059604645, 0.0, 0.0, 6.123234262925839e-17, -1.0, 0.0, 0.0, 0.0, 0.0, 0.14384864270687103, 0.9895997047424316]}}, {"episode": 8, "score": -5.084987223148346, "length": 25, "success": false, "info": {"speed": -4.537106606927182, "crashed": true, "action": [0.1453443467617035, 0.4181217551231384], "is_success": false, "path_distance": 8.076039119709053, "path_index": 1, "path_following_reward": -0.1, "action_change": 0.0, "smoothness_reward": -0.0121746106147766, "is_smooth_action": true, "reward_breakdown": {"base_reward": -5.395937804239215, "path_reward": -0.1, "smoothness_reward": -0.0121746106147766, "parking_reward": 0.0, "collision_avoidance_reward": -3.5615009264111266, "total_reward": -1.6408388914017284}, "processed_action": [0.1453443467617035, 0.4181217551231384], "episode_step": 25, "episode_score": -5.084987245715414, "reset_obs": [0.0, 0.0, -0.0, 0.0, -0.8615846037864685, 0.507614016532898, -0.2199999988079071, -0.14000000059604645, 0.0, 0.0, 6.123234262925839e-17, -1.0, 0.0, 0.0, -0.0, 0.0, -0.8615846037864685, 0.507614016532898]}}, {"episode": 9, "score": -12.083819530904293, "length": 43, "success": false, "info": {"speed": 3.4771795215706014, "crashed": true, "action": [-0.8654553294181824, 0.04179760813713074], "is_success": false, "path_distance": 4.520574361900693, "path_index": 14, "path_following_reward": -0.02520574361900693, "action_change": 0.0, "smoothness_reward": -0.00770927906036376, "is_smooth_action": true, "reward_breakdown": {"base_reward": -5.19626210679071, "path_reward": -0.02520574361900693, "smoothness_reward": -0.00770927906036376, "parking_reward": 0.0, "collision_avoidance_reward": -5.667278098817892, "total_reward": -1.9076162589027468}, "processed_action": [-0.8654553294181824, 0.04179760813713074], "episode_step": 43, "episode_score": -12.083819501418917, "reset_obs": [0.0, 0.0, 0.0, 0.0, 0.36787551641464233, 0.9298750758171082, -0.05999999865889549, -0.14000000059604645, 0.0, 0.0, 6.123234262925839e-17, -1.0, 0.0, 0.0, 0.0, 0.0, 0.36787551641464233, 0.9298750758171082]}}, {"episode": 10, "score": -0.3479871656745672, "length": 19, "success": true, "info": {"speed": 4.86145397461951, "crashed": false, "action": [-0.031023675575852394, -0.07671797275543213], "is_success": true, "path_distance": 0.17477695742286065, "path_index": 6, "path_following_reward": 0.19475669127731418, "action_change": 0.0, "smoothness_reward": 0.016, "is_smooth_action": true, "reward_breakdown": {"base_reward": -0.11723044352586129, "path_reward": 0.19475669127731418, "smoothness_reward": 0.016, "parking_reward": -0.2, "collision_avoidance_reward": 0.0, "total_reward": -0.024156915885843715}, "processed_action": [-0.031023675575852394, -0.07671797275543213], "episode_step": 19, "episode_score": -0.3479871657123633, "reset_obs": [0.0, 0.0, 0.0, 0.0, 0.6840829253196716, 0.7294042706489563, 0.25999999046325684, 0.14000000059604645, 0.0, 0.0, 6.123234262925839e-17, 1.0, 0.0, 0.0, 0.0, 0.0, 0.6840829253196716, 0.7294042706489563]}}], "config": {"max_episode_steps": 150, "render": true, "render_mode": "human", "device": "cuda:0"}}