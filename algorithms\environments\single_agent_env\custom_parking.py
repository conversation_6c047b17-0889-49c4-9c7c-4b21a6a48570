# ============================================================================
# 在导入任何其他模块之前先设置警告过滤
# ============================================================================
import warnings
import os

# 禁用所有警告
warnings.filterwarnings('ignore')

# 特别是禁用 gymnasium 的警告
os.environ['GYMNASIUM_DISABLE_WARNINGS'] = 'True'

# 禁用特定的警告
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", message=".*already in registry.*")

import numpy as np
import gymnasium as gym
from HighwayEnv.highway_env.envs.parking_env import ParkingEnv
from typing import Optional, List, Tuple, Dict, Any, Union

# 类型别名
State = Tuple[float, float, float]  # (x, y, theta)
Path = List[State]
Motion = Tuple[float, float]  # (distance, steering_angle)
import heapq
import sys
import os

# 添加HybridAstarPlanner路径
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), '../../../HybridAstarPlanner'))


# 移除专业版本导入，使用简化版本


class SimpleHybridAStar:
    """极简但有效的Hybrid A*路径规划器"""

    def __init__(self):
        # 简化参数
        self.step_size = 2.0
        self.wheelbase = 2.5

        # 简化的运动原语
        self.motions = [
            (2.0, 0),  # 直行
            (2.0, 0.3),  # 左转
            (2.0, -0.3),  # 右转
            (-1.5, 0),  # 后退
            (-1.5, 0.3),  # 左转后退
            (-1.5, -0.3),  # 右转后退
        ]

    def plan(self, start_state: State, goal_state: State, obstacles: List[dict]) -> Optional[Path]:
        """主路径规划接口：根据起点终点生成可行驾驶轨迹"""

        # 策略1：尝试基于车辆运动学的智能路径生成
        reasonable_path = self._generate_smart_path(start_state, goal_state, obstacles)
        if reasonable_path and len(reasonable_path) > 3:  # 检查路径质量
            return reasonable_path

        # 策略2：使用Hybrid A*搜索算法
        path = self._hybrid_astar_search(start_state, goal_state, obstacles)
        if path and len(path) > 3:  # 检查搜索结果质量
            return path

        # 策略3：生成简单直线路径作为备选
        return self._generate_simple_path(start_state, goal_state)

    def _simple_astar(self, start_state, goal_state, obstacles):
        """真正的Hybrid A*搜索 - 考虑车辆运动学"""
        open_list = []
        closed_set = set()
        came_from = {}
        g_costs = {}

        start_node = self._state_to_node(start_state)
        g_costs[start_node] = 0
        h_cost = self._heuristic(start_state, goal_state)
        heapq.heappush(open_list, (h_cost, start_node, start_state))

        for iteration in range(150):  # 适中的迭代次数
            if not open_list:
                break

            _, current_node, current_state = heapq.heappop(open_list)

            if current_node in closed_set:
                continue
            closed_set.add(current_node)

            # 目标检查
            if self._is_goal_reached(current_state, goal_state):
                path = self._reconstruct_path(came_from, current_node, start_node)
                path.append(current_state)  # 添加目标状态
                return path

            # 扩展邻居 - 使用车辆运动学
            for motion in self.motions:
                new_state = self._apply_motion_with_kinematics(current_state, motion)
                new_node = self._state_to_node(new_state)

                if new_node not in closed_set and self._is_collision_free(new_state, obstacles):
                    # 计算真实代价
                    motion_cost = self._motion_cost(motion)
                    tentative_g_cost = g_costs[current_node] + motion_cost

                    if new_node not in g_costs or tentative_g_cost < g_costs[new_node]:
                        g_costs[new_node] = tentative_g_cost
                        h_cost = self._heuristic(new_state, goal_state)
                        f_cost_new = tentative_g_cost + h_cost

                        came_from[new_node] = (current_node, new_state)
                        heapq.heappush(open_list, (f_cost_new, new_node, new_state))

        return None

    def _generate_simple_path(self, start_state, goal_state):
        """生成简单路径"""
        x1, y1, theta1 = start_state
        x2, y2, theta2 = goal_state

        # 添加一个中间点
        mid_x = (x1 + x2) / 2
        mid_y = (y1 + y2) / 2
        mid_theta = (theta1 + theta2) / 2

        return [start_state, (mid_x, mid_y, mid_theta), goal_state]

    def _generate_smart_path(self, start_state: State, goal_state: State, obstacles: List[dict]) -> Optional[Path]:
        """简化路径生成：只提供关键拐点，让智能体自主决定如何到达"""
        try:
            # 解析起点和终点状态
            x1, y1, theta1 = start_state  # 起点坐标和朝向
            x2, y2, theta2 = goal_state   # 终点坐标和朝向

            # 计算起点到终点的直线距离
            distance = np.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)

            # 简化策略：不考虑车头朝向，只提供几个关键点
            # 让智能体自己学会如何转弯和机动
            if distance < 5.0:
                # 距离较近，直接连接
                return [start_state, goal_state]
            else:
                # 距离较远，添加一个中间拐点
                # 中间点选择：在起点和终点之间，稍微偏移避免直线
                mid_x = (x1 + x2) / 2
                mid_y = (y1 + y2) / 2

                # 添加小幅偏移，避免完全直线
                offset = min(distance * 0.1, 3.0)  # 最大3米偏移
                mid_x += offset * np.cos(theta1 + np.pi/2)  # 垂直于起始朝向
                mid_y += offset * np.sin(theta1 + np.pi/2)

                # 中间点朝向：指向目标
                mid_theta = np.arctan2(y2 - mid_y, x2 - mid_x)

                return [start_state, (mid_x, mid_y, mid_theta), goal_state]

        except Exception:
            # 异常情况返回简单路径
            return [start_state, goal_state]

    def _generate_forward_path(self, start_state: State, goal_state: State) -> Path:
        """生成前进路径（小角度调整）"""
        x1, y1, theta1 = start_state
        x2, y2, theta2 = goal_state

        path = [start_state]

        # 添加3-5个中间点，逐步调整朝向
        num_points = 4
        for i in range(1, num_points):
            ratio = i / num_points

            # 位置插值
            x = x1 + ratio * (x2 - x1)
            y = y1 + ratio * (y2 - y1)

            # 朝向平滑插值
            theta = theta1 + ratio * (theta2 - theta1)
            theta = np.arctan2(np.sin(theta), np.cos(theta))

            path.append((x, y, theta))

        path.append(goal_state)
        return path

    def _generate_arc_path(self, start_state: State, goal_state: State) -> Path:
        """生成弧线路径（中等角度）"""
        x1, y1, theta1 = start_state
        x2, y2, theta2 = goal_state

        # 计算转弯中心
        distance = np.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)

        # 使用较大的转弯半径确保平滑
        turn_radius = max(distance / 2, self.wheelbase * 3)

        path = [start_state]

        # 生成弧线上的点
        num_points = max(6, int(distance / 2))  # 根据距离调整点数

        for i in range(1, num_points):
            ratio = i / num_points

            # 使用三次贝塞尔曲线生成平滑路径
            # 控制点设计确保符合车辆运动学
            p0 = np.array([x1, y1])
            p3 = np.array([x2, y2])

            # 控制点基于起始和结束朝向
            control_distance = distance / 3
            p1 = p0 + control_distance * np.array([np.cos(theta1), np.sin(theta1)])
            p2 = p3 - control_distance * np.array([np.cos(theta2), np.sin(theta2)])

            # 贝塞尔曲线计算
            t = ratio
            pos = (1 - t) ** 3 * p0 + 3 * (1 - t) ** 2 * t * p1 + 3 * (1 - t) * t ** 2 * p2 + t ** 3 * p3

            # 计算该点的朝向（切线方向）
            if i < num_points - 1:
                # 计算切线方向
                derivative = 3 * (1 - t) ** 2 * (p1 - p0) + 6 * (1 - t) * t * (p2 - p1) + 3 * t ** 2 * (p3 - p2)
                theta = np.arctan2(derivative[1], derivative[0])
            else:
                theta = theta2

            path.append((pos[0], pos[1], theta))

        path.append(goal_state)
        return path

    def _generate_complex_path(self, start_state: State, goal_state: State) -> Path:
        """生成复杂机动路径（大角度或倒车）"""
        x1, y1, theta1 = start_state
        x2, y2, theta2 = goal_state

        path = [start_state]

        # 策略：先转向，再移动，最后调整
        distance = np.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)

        # 第一阶段：原地转向调整
        target_direction = np.arctan2(y2 - y1, x2 - x1)
        intermediate_theta = target_direction

        # 添加转向过程的中间点
        for i in range(1, 4):
            ratio = i / 4
            theta = theta1 + ratio * (intermediate_theta - theta1)
            theta = np.arctan2(np.sin(theta), np.cos(theta))
            path.append((x1, y1, theta))

        # 第二阶段：沿目标方向移动
        num_move_points = max(4, int(distance / 3))
        for i in range(1, num_move_points):
            ratio = i / num_move_points
            x = x1 + ratio * (x2 - x1)
            y = y1 + ratio * (y2 - y1)
            path.append((x, y, intermediate_theta))

        # 第三阶段：最终朝向调整
        for i in range(1, 4):
            ratio = i / 4
            theta = intermediate_theta + ratio * (theta2 - intermediate_theta)
            theta = np.arctan2(np.sin(theta), np.cos(theta))
            path.append((x2, y2, theta))

        path.append(goal_state)
        return path

    def _apply_motion_with_kinematics(self, state: Tuple[float, float, float],
                                      motion: Tuple[float, float]) -> Tuple[float, float, float]:
        """使用车辆运动学模型应用运动（Hybrid A*核心）"""
        x, y, theta = state
        distance, steering_angle = motion

        # 使用自行车模型（bicycle model）- Hybrid A*的核心
        if abs(steering_angle) < 0.01:  # 直行
            new_x = x + distance * np.cos(theta)
            new_y = y + distance * np.sin(theta)
            new_theta = theta
        else:
            # 计算转弯半径（考虑轴距）
            turning_radius = self.wheelbase / np.tan(abs(steering_angle))

            # 计算角度变化
            delta_theta = distance / turning_radius
            if steering_angle < 0:  # 右转
                delta_theta = -delta_theta

            # 计算新位置（使用圆弧运动）
            if distance > 0:  # 前进
                new_x = x + turning_radius * (np.sin(theta + delta_theta) - np.sin(theta))
                new_y = y - turning_radius * (np.cos(theta + delta_theta) - np.cos(theta))
            else:  # 后退
                new_x = x - turning_radius * (np.sin(theta + delta_theta) - np.sin(theta))
                new_y = y + turning_radius * (np.cos(theta + delta_theta) - np.cos(theta))

            new_theta = theta + delta_theta

        # 规范化角度到[-π, π]
        new_theta = np.arctan2(np.sin(new_theta), np.cos(new_theta))

        return (new_x, new_y, new_theta)

    def _apply_motion(self, state: Tuple[float, float, float],
                      motion: Tuple[float, float]) -> Tuple[float, float, float]:
        """应用运动原语 - 使用车辆运动学模型"""
        x, y, theta = state
        distance, steering_angle = motion

        # 限制转向角
        steering_angle = np.clip(steering_angle, -self.max_steering_angle, self.max_steering_angle)

        # 使用自行车模型（bicycle model）
        if abs(steering_angle) < 0.01:  # 直行
            new_x = x + distance * np.cos(theta)
            new_y = y + distance * np.sin(theta)
            new_theta = theta
        else:
            # 计算转弯半径
            turning_radius = self.wheelbase / np.tan(abs(steering_angle))

            # 计算角度变化
            delta_theta = distance / turning_radius
            if steering_angle < 0:  # 右转
                delta_theta = -delta_theta

            # 计算新位置
            if distance > 0:  # 前进
                new_x = x + turning_radius * (np.sin(theta + delta_theta) - np.sin(theta))
                new_y = y - turning_radius * (np.cos(theta + delta_theta) - np.cos(theta))
            else:  # 后退
                new_x = x - turning_radius * (np.sin(theta + delta_theta) - np.sin(theta))
                new_y = y + turning_radius * (np.cos(theta + delta_theta) - np.cos(theta))

            new_theta = theta + delta_theta

        # 规范化角度到[-π, π]
        new_theta = np.arctan2(np.sin(new_theta), np.cos(new_theta))

        return (new_x, new_y, new_theta)

    def _is_collision_free(self, state: Tuple[float, float, float],
                           obstacles: List[dict]) -> bool:
        """简单的碰撞检测"""
        x, y, _ = state

        for obs in obstacles:
            distance = np.linalg.norm(np.array([x, y]) - obs['position'])
            if distance < 3.5:  # 简单的安全距离
                return False

        return True

    def _heuristic(self, state: Tuple[float, float, float],
                   goal: Tuple[float, float, float]) -> float:
        """简化的启发式函数 - 主要考虑位置距离"""
        x1, y1, theta1 = state
        x2, y2, theta2 = goal

        # 位置距离（主要因素）
        position_distance = np.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)

        # 简化的朝向代价
        final_angle_diff = abs(theta2 - theta1)
        final_angle_diff = min(final_angle_diff, 2 * np.pi - final_angle_diff)
        angle_cost = 0.5 * final_angle_diff  # 降低朝向权重

        # 简化的组合代价
        return position_distance + angle_cost

    def _is_goal_reached(self, state: Tuple[float, float, float],
                         goal: Tuple[float, float, float]) -> bool:
        """检查是否到达目标 - 放宽条件提高搜索成功率"""
        x1, y1, theta1 = state
        x2, y2, theta2 = goal

        # 位置容差 - 进一步放宽
        position_distance = np.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)
        position_tolerance = 4.0  # 4.0米位置容差

        # 朝向容差 - 进一步放宽
        angle_diff = abs(theta2 - theta1)
        angle_diff = min(angle_diff, 2 * np.pi - angle_diff)
        angle_tolerance = np.pi / 2  # 90度朝向容差

        # 同时满足位置和朝向要求
        return position_distance < position_tolerance and angle_diff < angle_tolerance

    def _state_to_node(self, state: Tuple[float, float, float]) -> Tuple[int, int, int]:
        """将状态转换为节点（用于哈希）- 降低精度提高搜索效率"""
        x, y, theta = state

        # 位置离散化：1米精度（降低精度）
        x_discrete = round(x)
        y_discrete = round(y)

        # 朝向离散化：30度精度（12个方向，降低精度）
        theta_normalized = np.arctan2(np.sin(theta), np.cos(theta))  # 规范化到[-π, π]
        theta_discrete = round(theta_normalized / (np.pi / 6)) * (np.pi / 6)

        return (int(x_discrete), int(y_discrete), int(theta_discrete / (np.pi / 6)))

    def _motion_cost(self, motion: Tuple[float, float]) -> float:
        """改进的运动代价 - 考虑距离、转向和方向"""
        distance, steering_angle = motion

        # 基础距离代价
        distance_cost = abs(distance)

        # 转向代价：转向角度越大代价越高
        steering_cost = 0.8 * abs(steering_angle)

        # 后退代价：后退比前进代价更高
        reverse_penalty = 0.3 if distance < 0 else 0

        # 急转弯代价：大角度转向额外惩罚
        sharp_turn_penalty = 0.5 if abs(steering_angle) > 0.4 else 0

        return distance_cost + steering_cost + reverse_penalty + sharp_turn_penalty

    def _reconstruct_path(self, came_from: dict, current: Tuple,
                          start: Tuple) -> List[Tuple[float, float, float]]:
        """重构路径"""
        path = []
        while current in came_from:
            parent_node, state = came_from[current]
            path.append(state)
            current = parent_node
            if current == start:
                break
        path.reverse()
        return path

    def _generate_intermediate_path(self, start_state: Tuple[float, float, float],
                                    goal_state: Tuple[float, float, float]) -> List[Tuple[float, float, float]]:
        """生成带中间点的路径"""
        x1, y1, theta1 = start_state
        x2, y2, theta2 = goal_state

        # 计算距离和方向
        distance = np.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)

        # 如果距离很近，直接连接
        if distance < 5.0:
            return [start_state, goal_state]

        # 生成中间点
        path = [start_state]

        # 添加1-2个中间点
        num_intermediate = min(2, int(distance / 8.0))

        for i in range(1, num_intermediate + 1):
            ratio = i / (num_intermediate + 1)

            # 线性插值位置
            x_mid = x1 + ratio * (x2 - x1)
            y_mid = y1 + ratio * (y2 - y1)

            # 插值朝向
            theta_mid = theta1 + ratio * (theta2 - theta1)

            # 规范化角度
            theta_mid = np.arctan2(np.sin(theta_mid), np.cos(theta_mid))

            path.append((x_mid, y_mid, theta_mid))

        path.append(goal_state)
        return path


class CustomParkingEnv(ParkingEnv):
    """
    自定义停车环境，继承自 HighwayEnv 的 ParkingEnv
    集成基础版Hybrid A*路径规划和平滑度控制
    """

    def __init__(self, config):
        # 保存原始配置
        self.custom_config = config
        self.num_envs = 1

        # 初始化扁平化标志
        self._flatten_obs = False

        # 初始化路径规划器和相关变量
        self.path_planner = SimpleHybridAStar()
        self.reference_path = None
        self.path_index = 0
        self.last_action = None
        self.last_raw_action = None  # 用于动作平滑

        # 准备传递给父类的配置
        env_config = self._prepare_config(config)
        render_mode = getattr(config, 'render_mode', 'human')

        # 调用父类初始化
        super(CustomParkingEnv, self).__init__(config=env_config, render_mode=render_mode)

        # 处理观察空间 - 如果是Dict类型，转换为扁平化的Box
        self._setup_observation_space()

        # 使用父类的默认动作空间，不做额外限制

        # 设置最大步数
        self.max_episode_steps = getattr(config, 'max_episode_steps', 100)

    def _prepare_config(self, config):
        """准备传递给父类的配置"""
        # 获取默认配置
        env_config = self.default_config()

        # 更新自定义配置
        if hasattr(config, 'env_seed'):
            env_config['seed'] = config.env_seed

        # 支持自定义episode最大步数
        if hasattr(config, 'max_episode_steps') and config.max_episode_steps is not None:
            env_config['duration'] = config.max_episode_steps

        # 支持自定义碰撞惩罚
        if hasattr(config, 'collision_reward') and config.collision_reward is not None:
            env_config['collision_reward'] = config.collision_reward

        return env_config

    def _setup_observation_space(self):
        """设置观察空间，支持扁平化"""
        if isinstance(self.observation_space, gym.spaces.Dict):
            # 计算总的观察维度
            total_dim = 0
            for space in self.observation_space.spaces.values():
                if isinstance(space, gym.spaces.Box):
                    total_dim += np.prod(space.shape)

            # 创建扁平化的观察空间
            self.observation_space = gym.spaces.Box(
                low=-np.inf,
                high=np.inf,
                shape=(total_dim,),
                dtype=np.float32
            )
            self._flatten_obs = True
        else:
            self._flatten_obs = False

    def _flatten_observation(self, obs):
        """扁平化观察"""
        if self._flatten_obs and isinstance(obs, dict):
            # 将Dict观察扁平化为一维数组
            flat_obs = []
            for key in sorted(obs.keys()):  # 保证顺序一致
                if isinstance(obs[key], np.ndarray):
                    flat_obs.append(obs[key].flatten())
                else:
                    flat_obs.append(np.array([obs[key]]).flatten())
            return np.concatenate(flat_obs).astype(np.float32)
        return obs

    def reset(self, *, seed: Optional[int] = None, options: Optional[dict] = None):
        """重置环境并规划路径"""
        # 如果提供了种子，使用自定义配置中的种子
        if seed is None and hasattr(self.custom_config, 'env_seed'):
            seed = self.custom_config.env_seed

        # 为了确保每次重置时有不同的随机性，在基础种子上添加时间戳
        if seed is not None:
            import time
            seed = seed + int(time.time() * 1000) % 10000

        obs, info = super().reset(seed=seed, options=options)

        # 规划路径
        self._plan_path()

        # 重置平滑度控制变量
        self.last_action = None
        self.last_raw_action = None
        self.path_index = 0

        # 重置停车检测计数器
        self.parking_detection_steps = 0

        obs = self._flatten_observation(obs)

        # 添加路径信息到info中
        if self.reference_path:
            info.update({
                'path_length': len(self.reference_path),
                'path_planned': True
            })
        else:
            info.update({
                'path_length': 0,
                'path_planned': False
            })

        return obs, info



    def _plan_path(self):
        """A* + 凸包组合路径规划"""
        try:
            vehicle = self.controlled_vehicles[0]
            start_state = (vehicle.position[0], vehicle.position[1], vehicle.heading)
            goal_state = (vehicle.goal.position[0], vehicle.goal.position[1], vehicle.goal.heading)

            # 获取障碍物
            obstacles = self._get_obstacles()

            # 1. 首先用A*规划详细路径
            detailed_path = self.path_planner.plan(start_state, goal_state, obstacles)

            if detailed_path is not None and len(detailed_path) > 2:
                # 2. 用凸包简化A*路径为关键拐弯点
                self.reference_path = self._simplify_path_with_convex_hull(detailed_path)
            else:
                # A*失败时，使用简单的关键点方法
                self.reference_path = self._generate_key_waypoints(
                    vehicle.position, vehicle.heading,
                    vehicle.goal.position, vehicle.goal.heading
                )

        except Exception:
            # 出错时使用最简单的两点路径
            vehicle = self.controlled_vehicles[0]
            start_state = (vehicle.position[0], vehicle.position[1], vehicle.heading)
            goal_state = (vehicle.goal.position[0], vehicle.goal.position[1], vehicle.goal.heading)
            self.reference_path = [start_state, goal_state]

    def _generate_key_waypoints(self, start_pos, start_heading, goal_pos, goal_heading):
        """生成关键拐弯点路径"""
        waypoints = []

        # 起点
        start_state = (start_pos[0], start_pos[1], start_heading)
        waypoints.append(start_state)

        # 计算起点到终点的距离和方向
        dx = goal_pos[0] - start_pos[0]
        dy = goal_pos[1] - start_pos[1]
        distance = np.sqrt(dx**2 + dy**2)
        direct_heading = np.arctan2(dy, dx)

        # 计算朝向差异
        heading_diff = abs(goal_heading - start_heading)
        heading_diff = min(heading_diff, 2*np.pi - heading_diff)

        # 根据情况决定是否需要中间拐弯点
        if distance > 8.0 or heading_diff > np.pi/3:  # 距离远或需要大幅转向
            # 添加一个中间拐弯点
            mid_x = start_pos[0] + dx * 0.6  # 60%位置处
            mid_y = start_pos[1] + dy * 0.6
            mid_heading = direct_heading  # 中间点朝向目标方向

            waypoints.append((mid_x, mid_y, mid_heading))

        # 终点
        goal_state = (goal_pos[0], goal_pos[1], goal_heading)
        waypoints.append(goal_state)

        return waypoints

    def _simplify_path_with_convex_hull(self, detailed_path):
        """用凸包简化A*规划的详细路径为关键拐弯点"""
        try:
            if len(detailed_path) <= 3:
                return detailed_path  # 路径已经很简单了

            # 提取路径点的位置信息（忽略朝向）
            points = [(x, y) for x, y, _ in detailed_path]

            # 计算凸包
            hull_points = self._compute_convex_hull(points)

            if len(hull_points) <= 2:
                return detailed_path  # 凸包计算失败，返回原路径

            # 将凸包点按照原路径的顺序排列
            simplified_path = self._order_hull_points_by_path(hull_points, detailed_path)

            return simplified_path

        except:
            return detailed_path  # 出错时返回原路径

    def _compute_convex_hull(self, points):
        """计算凸包（使用Graham扫描算法的简化版本）"""
        if len(points) < 3:
            return points

        # 找到最下方的点（y最小，如果相同则x最小）
        start_point = min(points, key=lambda p: (p[1], p[0]))

        # 按照极角排序其他点
        def polar_angle(p):
            dx = p[0] - start_point[0]
            dy = p[1] - start_point[1]
            return np.arctan2(dy, dx)

        other_points = [p for p in points if p != start_point]
        sorted_points = sorted(other_points, key=polar_angle)

        # 简化的凸包计算
        hull = [start_point]

        for point in sorted_points:
            # 移除不在凸包上的点
            while len(hull) > 1 and self._cross_product(hull[-2], hull[-1], point) <= 0:
                hull.pop()
            hull.append(point)

        return hull

    def _cross_product(self, o, a, b):
        """计算向量叉积，用于判断转向"""
        return (a[0] - o[0]) * (b[1] - o[1]) - (a[1] - o[1]) * (b[0] - o[0])

    def _order_hull_points_by_path(self, hull_points, original_path):
        """按照原始路径的顺序排列凸包点"""
        # 确保起点和终点在简化路径中
        start_point = (original_path[0][0], original_path[0][1])
        end_point = (original_path[-1][0], original_path[-1][1])

        # 构建简化路径
        simplified = []

        # 添加起点
        simplified.append(original_path[0])  # 保持朝向信息

        # 添加中间的凸包点（按照在原路径中出现的顺序）
        for i, (x, y, theta) in enumerate(original_path[1:-1], 1):
            point = (x, y)
            if point in hull_points and point != start_point and point != end_point:
                simplified.append((x, y, theta))

        # 添加终点
        if len(simplified) == 1 or simplified[-1] != original_path[-1]:
            simplified.append(original_path[-1])  # 保持朝向信息

        return simplified

    def _get_obstacles(self):
        """获取障碍物信息"""
        obstacles = []

        try:
            # 其他车辆作为障碍物
            for vehicle in self.road.vehicles:
                if vehicle != self.controlled_vehicles[0]:
                    obstacles.append({
                        'position': vehicle.position,
                        'type': 'vehicle'
                    })
        except:
            pass  # 如果获取障碍物失败，返回空列表

        return obstacles

    def _get_distance_to_path(self):
        """获取车辆到规划路径的最小距离和最近路径点信息"""
        # 检查是否存在有效的规划路径
        if not self.reference_path:
            return float('inf'), -1, None  # 无路径时返回无穷大距离

        try:
            # 获取当前被控制的车辆对象
            vehicle = self.controlled_vehicles[0]

            # 初始化搜索变量
            min_distance = float('inf')    # 最小距离，初始为无穷大
            closest_index = 0              # 最近路径点的索引
            closest_waypoint = None        # 最近的路径点坐标

            # 遍历规划路径中的每个路径点
            for i, waypoint in enumerate(self.reference_path):
                # 计算车辆当前位置到路径点的欧几里得距离
                # waypoint[:2] 提取路径点的x,y坐标（忽略朝向角度）
                # vehicle.position 是车辆当前的[x, y]坐标
                distance = np.linalg.norm(vehicle.position - np.array(waypoint[:2]))

                # 如果当前距离小于已记录的最小距离，更新最近点信息
                if distance < min_distance:
                    min_distance = distance      # 更新最小距离
                    closest_index = i           # 更新最近点索引
                    closest_waypoint = waypoint # 更新最近点坐标

            # 返回三个值：最小距离、最近点索引、最近点坐标
            return min_distance, closest_index, closest_waypoint

        except:
            # 异常情况下返回安全默认值
            return float('inf'), -1, None

    def step(self, action):
        """执行动作并获取下一个观察、奖励和其他信息"""
        # 最简单的解决方案：非对称动作映射
        action = np.array(action).copy()
        if action[0] > 0:
            action[0] *= 0.6  # 加速变弱：最大3.0 m/s²
        # 刹车保持原样：最大5.0 m/s²

        # 简单的动作平滑
        original_action = np.array(action)
        if hasattr(self, 'last_raw_action') and self.last_raw_action is not None:
            action = 0.7 * np.array(action) + 0.3 * np.array(self.last_raw_action)
        self.last_raw_action = original_action.copy()

        obs, reward, terminated, truncated, info = super().step(action)

        # 简单的速度惩罚（保持原有逻辑）
        vehicle = self.controlled_vehicles[0]
        if abs(vehicle.speed) > 6.0:
            reward -= 0.05 * (abs(vehicle.speed) - 6.0)

        # 注意：停车奖励现在在_compute_enhanced_parking_reward中处理，这里不再重复

        # 新的终止条件：只有真正停车才算成功
        terminated, truncated = self._check_parking_termination(terminated, truncated)

        # 回合结束后添加延迟
        if terminated or truncated:
            self._add_episode_end_delay()

        obs = self._flatten_observation(obs)

        # 添加详细的调试信息到info中
        try:
            # 计算各项奖励分量
            base_reward_value = super()._reward(action)
            path_reward_value = self._compute_path_following_reward()
            smoothness_reward_value = self._compute_smoothness_reward(action)
            parking_reward_value = self._compute_enhanced_parking_reward(action)

            if self.reference_path:
                vehicle = self.controlled_vehicles[0]
                min_dist = min(np.linalg.norm(vehicle.position - np.array(wp[:2]))
                               for wp in self.reference_path)

                info.update({
                    'path_distance': min_dist,
                    'path_index': self.path_index,
                    'path_following_reward': path_reward_value
                })

            # 添加平滑度和动作信息
            if self.last_action is not None:
                action_change = np.linalg.norm(np.array(action) - np.array(self.last_action))
                info.update({
                    'action_change': action_change,
                    'smoothness_reward': smoothness_reward_value,
                    'is_smooth_action': action_change < 0.3
                })

            # 添加奖励分解信息（包含新的停车奖励）
            info.update({
                'reward_breakdown': {
                    'base_reward': base_reward_value,
                    'path_reward': path_reward_value,
                    'smoothness_reward': smoothness_reward_value,
                    'parking_reward': parking_reward_value,  # 停车奖励
                    'total_reward': reward
                },
                'processed_action': action  # 处理后的动作
            })
        except:
            pass  # 如果添加信息失败，不影响主要功能

        # 标记需要重新绘制路径
        self._need_redraw_path = True

        return obs, reward, terminated, truncated, info

    def _add_episode_end_delay(self):
        """在回合结束后添加3秒延迟"""
        try:
            import time
            if getattr(self, 'debug', False):
                print("🕐 回合结束，延迟3秒以观察结果...")
            time.sleep(3.0)
        except:
            pass

    def _check_parking_termination(self, terminated, truncated):
        """检查停车终止条件：只有真正停车才算成功"""
        try:
            vehicle = self.controlled_vehicles[0]
            goal_distance = np.linalg.norm(vehicle.position - vehicle.goal.position)
            current_speed = abs(vehicle.speed)

            # 检查是否在目标区域内
            in_target_area = goal_distance < 1.5  # 1.5米内算目标区域

            # 检查是否基本停车（速度接近0）
            is_stopped = current_speed < 0.5  # 0.5m/s以下算停车

            # 检查朝向是否基本正确
            heading_diff = abs(vehicle.heading - vehicle.goal.heading)
            heading_diff = min(heading_diff, 2*np.pi - heading_diff)
            heading_ok = heading_diff < np.pi/3  # 60度内算朝向正确

            if in_target_area and is_stopped:
                # 在目标区域内且基本停车，开始计数
                if not hasattr(self, 'parking_detection_steps'):
                    self.parking_detection_steps = 0
                self.parking_detection_steps += 1

                # 连续停车3步以上才算真正成功
                if self.parking_detection_steps >= 3:
                    if getattr(self, 'debug', False):
                        print(f"🎯 停车成功！距离={goal_distance:.2f}m, 速度={current_speed:.2f}m/s, "
                              f"连续停车{self.parking_detection_steps}步")
                    terminated = True  # 成功终止
                else:
                    if getattr(self, 'debug', False) and self.parking_detection_steps % 1 == 0:
                        print(f"🔄 停车检测中... ({self.parking_detection_steps}/3)")
                    terminated = False  # 继续检测
            else:
                # 不在停车状态，重置计数
                self.parking_detection_steps = 0

                # 如果原本就要终止（比如碰撞），保持终止
                if terminated:
                    termination_reason = self._get_termination_reason()
                    if getattr(self, 'debug', False):
                        print(f"❌ 非停车终止: {termination_reason}")

            return terminated, truncated

        except:
            # 异常情况保持原终止状态
            return terminated, truncated

    def _get_termination_reason(self):
        """获取终止原因用于调试"""
        try:
            vehicle = self.controlled_vehicles[0]
            goal_distance = np.linalg.norm(vehicle.position - vehicle.goal.position)

            # 检查各种可能的终止原因
            if vehicle.crashed:
                return f"碰撞 (crashed=True)"

            # 检查是否成功到达
            if hasattr(self, 'controlled_vehicles') and len(self.controlled_vehicles) > 0:
                # 使用父类的成功判断逻辑
                if goal_distance < 1.5:  # 假设成功距离是1.5米
                    return f"成功到达 (距离={goal_distance:.2f}m)"

            # 检查是否超出边界
            if abs(vehicle.position[0]) > 50 or abs(vehicle.position[1]) > 50:
                return f"超出边界 (位置={vehicle.position})"

            # 检查速度是否过高导致失控
            if abs(vehicle.speed) > 15.0:
                return f"速度过高 (速度={vehicle.speed:.2f}m/s)"

            # 其他未知原因
            return f"未知原因 (距离={goal_distance:.2f}m, 速度={vehicle.speed:.2f}m/s, 碰撞={vehicle.crashed})"

        except Exception as e:
            return f"检查异常: {str(e)}"

    # ============================================================================
    # 奖励和碰撞相关方法 - 继承自父类ParkingEnv
    # ============================================================================

    def _reward(self, action: np.ndarray) -> float:
        """改进的奖励函数：基础奖励 + 路径跟踪 + 平滑度 + 强化停车奖励"""
        # 获取环境基础奖励（距离、成功、碰撞等）
        base_reward = super()._reward(action)

        # 计算路径跟踪奖励（鼓励沿规划路径行驶）
        path_reward = self._compute_path_following_reward()

        # 计算动作平滑度奖励（鼓励平滑驾驶）
        smoothness_reward = self._compute_smoothness_reward(action)

        # 计算强化停车奖励（重点解决停车问题）
        parking_reward = self._compute_enhanced_parking_reward(action)

        # 权重组合 - 增加停车奖励权重
        total_reward = (
                0.25 * base_reward +      # 基础任务完成奖励
                0.3 * path_reward +       # 路径跟踪奖励
                0.2 * smoothness_reward + # 平滑驾驶奖励
                0.25 * parking_reward     # 强化停车奖励
        )

        return total_reward

    def _should_demonstrate_parking(self):
        """判断是否需要进行停车示范（课程学习）"""
        try:
            vehicle = self.controlled_vehicles[0]
            goal_distance = np.linalg.norm(vehicle.position - vehicle.goal.position)
            current_speed = abs(vehicle.speed)

            # 在目标点附近且速度较高时，提供停车示范
            if goal_distance < 1.5 and current_speed > 1.0:
                return True
            return False
        except:
            return False

    def _get_parking_demonstration_action(self):
        """提供停车示范动作（非强制，仅作为奖励引导）"""
        try:
            vehicle = self.controlled_vehicles[0]
            goal_distance = np.linalg.norm(vehicle.position - vehicle.goal.position)
            current_speed = abs(vehicle.speed)

            # 根据距离和速度提供建议的停车动作
            if goal_distance < 0.8:  # 非常接近
                if current_speed > 0.5:
                    return np.array([-0.6, 0.0])  # 强烈建议刹车
                else:
                    return np.array([0.0, 0.0])   # 建议保持静止
            elif goal_distance < 1.5:  # 接近
                if current_speed > 1.0:
                    return np.array([-0.4, 0.0])  # 建议减速
                else:
                    return np.array([0.1, 0.0])   # 建议慢速接近

            return None  # 不需要示范
        except:
            return None

    def _compute_enhanced_parking_reward(self, action):
        """强化停车奖励：通过奖励塑形教会智能体停车概念"""
        try:
            vehicle = self.controlled_vehicles[0]
            goal_distance = np.linalg.norm(vehicle.position - vehicle.goal.position)
            current_speed = abs(vehicle.speed)
            acceleration = action[0] if len(action) > 0 else 0.0

            parking_reward = 0.0

            # 1. 渐进式停车教学：距离越近，对速度要求越严格
            if goal_distance < 1.0:  # 最终停车区域
                # 教会智能体：在目标点必须停车
                if current_speed < 0.2:  # 基本停止
                    parking_reward += 1.0  # 非常大的奖励
                    # 额外奖励保持停车状态
                    if abs(acceleration) < 0.1:  # 没有明显加速
                        parking_reward += 0.5
                elif current_speed < 0.5:  # 很慢
                    parking_reward += 0.3
                    if acceleration <= 0:  # 正在减速
                        parking_reward += 0.4
                else:  # 在停车区域内还在快速移动
                    parking_reward -= 1.0  # 强烈惩罚

            elif goal_distance < 3.0:  # 接近目标（3米内）
                # 准备停车阶段，鼓励减速
                if current_speed < 1.0:  # 低速接近
                    parking_reward += 0.15
                    if acceleration <= 0:  # 正在减速
                        parking_reward += 0.15
                elif current_speed < 2.0:  # 适中速度
                    parking_reward += 0.05
                    if acceleration <= 0:  # 正在减速
                        parking_reward += 0.1
                else:  # 速度过快
                    parking_reward -= 0.2

            elif goal_distance < 5.0:  # 较远但需要开始准备（5米内）
                # 开始减速阶段
                if current_speed < 2.0 and acceleration <= 0:
                    parking_reward += 0.05  # 小奖励鼓励提前减速
                elif current_speed > 4.0:
                    parking_reward -= 0.1  # 惩罚高速接近

            # 2. 停车动作识别和教学
            if goal_distance < 2.0:  # 在停车准备区域
                # 教会智能体识别和执行停车动作
                if current_speed < 0.3 and abs(acceleration) < 0.1:
                    # 完美停车状态：低速 + 无加速
                    parking_reward += 0.8
                elif acceleration < -0.3:  # 主动刹车
                    parking_reward += 0.4  # 奖励刹车意识
                elif acceleration > 0.2 and current_speed > 1.0:  # 在停车区域加速
                    parking_reward -= 0.8  # 强烈惩罚错误行为

            # 3. 停车状态维持奖励（教会智能体保持停车）
            if goal_distance < 1.0 and current_speed < 0.3:
                # 在目标点附近保持低速状态
                if not hasattr(self, 'parking_state_steps'):
                    self.parking_state_steps = 0
                self.parking_state_steps += 1

                # 连续保持停车状态的递增奖励
                maintain_bonus = min(0.1 * self.parking_state_steps, 1.0)
                parking_reward += maintain_bonus
            else:
                # 离开停车状态，重置计数
                if hasattr(self, 'parking_state_steps'):
                    self.parking_state_steps = 0

            # 3. 连续停车奖励（鼓励保持停车状态）
            if not hasattr(self, 'consecutive_parking_steps'):
                self.consecutive_parking_steps = 0

            if goal_distance < 1.5 and current_speed < 0.3:
                self.consecutive_parking_steps += 1
                # 连续停车奖励递增
                consecutive_bonus = min(0.1 * self.consecutive_parking_steps, 0.5)
                parking_reward += consecutive_bonus
            else:
                self.consecutive_parking_steps = 0

            # 4. 朝向奖励（鼓励正确的停车朝向）
            if goal_distance < 2.0:
                heading_diff = abs(vehicle.heading - vehicle.goal.heading)
                heading_diff = min(heading_diff, 2*np.pi - heading_diff)
                if heading_diff < np.pi/6:  # 朝向基本正确（30度内）
                    parking_reward += 0.1
                elif heading_diff > np.pi/2:  # 朝向错误（90度以上）
                    parking_reward -= 0.1

            # 5. 停车示范学习奖励（非强制，通过奖励引导）
            if self._should_demonstrate_parking():
                demo_action = self._get_parking_demonstration_action()
                if demo_action is not None:
                    # 计算智能体动作与示范动作的相似度
                    action_similarity = self._compute_action_similarity(action, demo_action)
                    if action_similarity > 0.7:  # 动作相似
                        parking_reward += 0.3  # 奖励学习示范
                    elif action_similarity < 0.3:  # 动作相反
                        parking_reward -= 0.2  # 轻微惩罚

            return parking_reward

        except:
            return 0.0

    def _compute_action_similarity(self, action1, action2):
        """计算两个动作的相似度（0-1之间）"""
        try:
            # 确保动作是numpy数组
            a1 = np.array(action1)
            a2 = np.array(action2)

            # 计算欧几里得距离
            distance = np.linalg.norm(a1 - a2)

            # 转换为相似度（距离越小，相似度越高）
            # 假设最大可能距离为2.0（加速度范围-1到1，转向角度范围-1到1）
            max_distance = 2.0
            similarity = max(0, 1 - distance / max_distance)

            return similarity
        except:
            return 0.0

    def _compute_path_following_reward(self):
        """改进的导航奖励：注重朝向目标前进，而非严格跟随路径"""
        try:
            # 获取当前控制车辆
            vehicle = self.controlled_vehicles[0]

            # 计算朝向目标的奖励（主要奖励）
            goal_direction_reward = self._compute_goal_direction_reward()

            # 计算前进进度奖励
            progress_reward = self._compute_progress_reward()

            # 轻微的路径参考奖励（权重很小）
            path_reference_reward = self._compute_light_path_reference_reward()

            # 组合奖励：主要鼓励朝向目标，路径只是参考
            total_navigation_reward = (
                0.6 * goal_direction_reward +    # 主要：朝向目标
                0.3 * progress_reward +           # 重要：前进进度
                0.1 * path_reference_reward       # 次要：路径参考
            )

            return total_navigation_reward

        except:
            return 0.0

    def _compute_goal_direction_reward(self):
        """计算朝向目标的奖励：鼓励智能体朝正确方向前进"""
        try:
            vehicle = self.controlled_vehicles[0]

            # 计算从当前位置到目标的方向
            goal_direction = np.arctan2(
                vehicle.goal.position[1] - vehicle.position[1],
                vehicle.goal.position[0] - vehicle.position[0]
            )

            # 计算车头朝向与目标方向的角度差
            heading_diff = abs(vehicle.heading - goal_direction)
            heading_diff = min(heading_diff, 2*np.pi - heading_diff)  # 规范化到[0,π]

            # 计算朝向奖励
            if heading_diff < np.pi/6:  # 30度内，朝向很好
                direction_reward = 0.3
            elif heading_diff < np.pi/3:  # 60度内，朝向较好
                direction_reward = 0.2
            elif heading_diff < np.pi/2:  # 90度内，朝向一般
                direction_reward = 0.1
            else:  # 朝向错误
                direction_reward = -0.1

            # 如果在前进且朝向正确，额外奖励
            current_speed = vehicle.speed
            if current_speed > 0.5 and heading_diff < np.pi/3:
                direction_reward += 0.1  # 前进且朝向正确的额外奖励
            elif current_speed < -0.5:  # 倒车惩罚
                direction_reward -= 0.2

            return direction_reward

        except:
            return 0.0

    def _compute_light_path_reference_reward(self):
        """轻微的路径参考奖励：只作为辅助，不强制跟随"""
        if not self.reference_path or len(self.reference_path) < 2:
            return 0.0

        try:
            vehicle = self.controlled_vehicles[0]

            # 计算到路径的距离
            min_distance, _, _ = self._get_distance_to_path()

            # 很宽松的路径参考奖励
            if min_distance < 8.0:  # 8米内给小奖励
                return 0.05 - min_distance * 0.005  # 最大0.05分
            else:
                return -0.02  # 轻微惩罚

        except:
            return 0.0

    def _compute_progress_reward(self):
        """计算前进进度奖励：鼓励车辆沿路径前进"""
        if not hasattr(self, 'last_path_index'):
            self.last_path_index = 0
            return 0.0

        try:
            # 获取当前路径索引
            _, current_index, _ = self._get_distance_to_path()

            if current_index == -1:
                return 0.0

            # 计算路径进度变化
            progress_change = current_index - self.last_path_index

            # 更新记录
            self.last_path_index = current_index

            # 前进奖励
            if progress_change > 0:
                return 0.05 * progress_change  # 每前进一个路径点奖励0.05
            elif progress_change < 0:
                return -0.02 * abs(progress_change)  # 后退轻微惩罚
            else:
                return 0.0  # 停留在同一点无奖励无惩罚

        except:
            return 0.0

    def _compute_smoothness_reward(self, action):
        """细化的平滑度奖励：分别优化速度控制和转向控制"""
        # 首次调用时初始化上一次动作
        if self.last_action is None:
            self.last_action = action.copy() if hasattr(action, 'copy') else np.array(action)
            return 0.0

        try:
            # 分别计算速度和转向的平滑度奖励
            speed_reward = self._compute_speed_control_reward(action)
            steering_reward = self._compute_steering_control_reward(action)

            # 保存当前动作供下次比较
            self.last_action = action.copy() if hasattr(action, 'copy') else np.array(action)

            # 组合平滑度奖励（速度控制更重要）
            return 0.6 * speed_reward + 0.4 * steering_reward

        except:
            # 异常情况返回零奖励
            return 0.0

    def _compute_speed_control_reward(self, action):
        """速度控制奖励：鼓励合适的速度和平滑加速"""
        acceleration = action[0]
        last_acceleration = self.last_action[0]

        speed_penalty = 0.0

        # 1. 鼓励适中的速度（重点鼓励前进）
        if abs(acceleration) > 0.8:  # 速度过快
            speed_penalty += (abs(acceleration) - 0.8) * 0.4  # 惩罚
        elif abs(acceleration) > 0.6:  # 速度较快
            speed_penalty += (abs(acceleration) - 0.6) * 0.2  # 惩罚
        elif abs(acceleration) < 0.02:  # 几乎不动
            speed_penalty += 0.08  # 增加对不动的惩罚
        elif 0.1 < abs(acceleration) < 0.5:  # 适中速度
            speed_penalty -= 0.02  # 奖励适中的前进速度

        # 2. 平滑加速奖励（防止急加速/急减速）
        accel_change = abs(acceleration - last_acceleration)
        if accel_change > 0.4:  # 加速度变化过大
            speed_penalty += accel_change * 0.5
        elif accel_change < 0.1:  # 加速度变化平滑
            speed_penalty -= 0.02  # 小奖励

        # 3. 渐进式速度调整奖励
        if 0.1 < abs(acceleration) < 0.6:  # 适中的速度
            speed_penalty -= 0.03  # 奖励适中速度

        return -speed_penalty

    def _compute_steering_control_reward(self, action):
        """转向控制奖励：鼓励渐进式转向和有效转向"""
        if len(action) < 2:
            return 0.0

        steering = action[1]
        last_steering = self.last_action[1] if len(self.last_action) > 1 else 0

        steering_penalty = 0.0
        steering_bonus = 0.0

        # 1. 转向变化平滑度（防止急转向）
        steering_change = abs(steering - last_steering)
        if steering_change > 0.3:  # 转向变化过大
            steering_penalty += steering_change * 0.4
        elif steering_change < 0.05:  # 转向变化平滑
            steering_bonus += 0.01

        # 2. 渐进转向奖励（鼓励逐步调整）
        steer_magnitude = abs(steering)
        if 0.05 < steer_magnitude < 0.4:  # 适中的转向
            steering_bonus += 0.02
        elif steer_magnitude > 0.7:  # 过度转向
            steering_penalty += (steer_magnitude - 0.7) * 0.5

        # 3. 转向有效性奖励
        if self.reference_path:
            effectiveness = self._evaluate_steering_effectiveness(action)
            if effectiveness > 0.7:  # 高效转向
                steering_bonus += 0.03
            elif effectiveness < 0.3:  # 低效转向
                steering_penalty += 0.02

        # 4. 直线行驶时的转向惩罚
        if self._is_straight_path_ahead() and steer_magnitude > 0.2:
            steering_penalty += steer_magnitude * 0.3  # 直线时不应大幅转向

        return -steering_penalty + steering_bonus

    def _is_straight_path_ahead(self):
        """判断前方路径是否为直线（用于转向控制）"""
        if not self.reference_path or len(self.reference_path) < 3:
            return True  # 路径太短，默认为直线

        try:
            # 获取当前位置在路径中的索引
            _, current_index, _ = self._get_distance_to_path()

            # 检查前方3个路径点的角度变化
            if current_index >= 0 and current_index + 2 < len(self.reference_path):
                # 计算连续路径段的角度变化
                p1 = self.reference_path[current_index]
                p2 = self.reference_path[current_index + 1]
                p3 = self.reference_path[current_index + 2]

                # 计算两个路径段的方向角
                angle1 = np.arctan2(p2[1] - p1[1], p2[0] - p1[0])
                angle2 = np.arctan2(p3[1] - p2[1], p3[0] - p2[0])

                # 计算角度变化
                angle_change = abs(angle2 - angle1)
                angle_change = min(angle_change, 2*np.pi - angle_change)

                # 角度变化小于15度认为是直线
                return angle_change < np.pi/12

            return True

        except:
            return True

    def _evaluate_steering_effectiveness(self, action):
        """评估转向动作的有效性：判断转向是否朝向正确方向"""
        # 检查输入有效性
        if not self.reference_path or len(action) < 2:
            return 0.0

        try:
            # 获取当前车辆状态
            vehicle = self.controlled_vehicles[0]
            steering_angle = action[1]  # 转向角度（正值=左转，负值=右转）

            # 获取最近的路径点
            min_distance, _, closest_waypoint = self._get_distance_to_path()

            # 检查是否找到有效路径点
            if closest_waypoint is None:
                return 0.0

            # 计算从当前位置到目标路径点的方向
            target_direction = np.arctan2(
                closest_waypoint[1] - vehicle.position[1],  # y方向差
                closest_waypoint[0] - vehicle.position[0]   # x方向差
            )

            # 计算车头朝向与目标方向的角度差
            heading_diff = target_direction - vehicle.heading
            heading_diff = np.arctan2(np.sin(heading_diff), np.cos(heading_diff))  # 规范化到[-π,π]

            # 评估转向有效性
            if abs(heading_diff) < 0.1:  # 车头已经基本朝向目标（约6度内）
                # 不需要转向时，小转向=好，大转向=差
                return 1.0 if abs(steering_angle) < 0.1 else 0.2
            elif heading_diff > 0:  # 目标在左侧，需要左转
                # 左转有效性：正转向角度越大越好
                return max(0, steering_angle) / 0.6
            else:  # 目标在右侧，需要右转
                # 右转有效性：负转向角度绝对值越大越好
                return max(0, -steering_angle) / 0.6

        except:
            # 异常情况返回无效
            return 0.0

    def _draw_planned_path_on_surface(self, surface):
        """在指定surface上绘制规划路径"""
        try:
            # 检查基本条件
            if not self.reference_path or len(self.reference_path) < 2:
                return

            # 导入pygame
            import pygame

            # 转换路径点为屏幕坐标
            screen_points = []
            for waypoint in self.reference_path:
                x, y = waypoint[:2]
                screen_x, screen_y = surface.pos2pix(x, y)
                screen_points.append((int(screen_x), int(screen_y)))

            if len(screen_points) < 2:
                return

            # 绘制路径线（红色粗线 - 表示Hybrid A*规划路径）
            pygame.draw.lines(surface, (255, 50, 50), False, screen_points, 6)

            # 绘制起点（绿色圆圈）
            pygame.draw.circle(surface, (50, 255, 50), screen_points[0], 12)
            pygame.draw.circle(surface, (255, 255, 255), screen_points[0], 12, 2)

            # 绘制终点（蓝色方块 - 目标停车位）
            target_size = 10
            target_rect = pygame.Rect(
                screen_points[-1][0] - target_size,
                screen_points[-1][1] - target_size,
                target_size * 2,
                target_size * 2
            )
            pygame.draw.rect(surface, (50, 150, 255), target_rect)
            pygame.draw.rect(surface, (255, 255, 255), target_rect, 2)

            # 绘制路径点（小红色圆点）
            for i in range(1, len(screen_points) - 1):
                pygame.draw.circle(surface, (200, 100, 100), screen_points[i], 4)

        except Exception:
            pass  # 静默处理绘制错误

    # ============================================================================
    # 配置和默认设置相关方法 - 摄像头固定中央功能
    # ============================================================================

    @classmethod
    def default_config(cls) -> dict:
        """
        默认环境配置
        重写自 ParkingEnv，设置固定摄像头视角

        Returns:
            dict: 默认配置字典
        """
        config = super(CustomParkingEnv, cls).default_config()
        # 摄像头配置 - 固定在中央而不是跟随车辆
        config.update({
            "centering_position": [0.5, 0.5],  # 摄像头居中位置
            "scaling": 8,  # 缩放比例（增加以显示更多细节）
            "screen_width": 1200,  # 屏幕宽度（增加）
            "screen_height": 800,  # 屏幕高度（增加）
            # 禁用车辆跟随模式
            "offscreen_rendering": False,
        })
        return config

    def render(self, mode='human'):
        """
        重写渲染方法，设置固定摄像头视角并绘制路径
        """
        # 强制重新创建viewer以应用新的配置
        if self.viewer is None:
            from HighwayEnv.highway_env.envs.common.graphics import EnvViewer
            self.viewer = EnvViewer(self, config=self.config)

            # 使用agent_display机制来绘制路径
            def path_display(agent_surface, sim_surface):
                """使用agent_display机制绘制路径"""
                # agent_surface暂时不用，只在sim_surface上绘制
                _ = agent_surface  # 标记为故意未使用
                self._draw_planned_path_on_surface(sim_surface)

            # 设置agent_display
            self.viewer.set_agent_display(path_display)

        # 设置固定的观察者位置（摄像头固定在中央）
        self.viewer.observer_vehicle = None  # 清除跟随车辆

        def fixed_window_position():
            # 返回固定的中央位置 [0, 0] 表示停车场中心
            return np.array([0.0, 0.0])

        # 临时替换 window_position 方法
        self.viewer.window_position = fixed_window_position

        # 调用父类的渲染方法
        result = super().render()

        return result


def CustomParking_Env(config):
    """
    用于环境注册表的工厂函数

    Args:
        config: 环境配置参数

    Returns:
        CustomParkingEnv: 自定义停车环境实例
    """
    return CustomParkingEnv(config)
