#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试停车行为：验证延迟终止和停车奖励
"""

import warnings
import os
warnings.filterwarnings('ignore')
os.environ['GYMNASIUM_DISABLE_WARNINGS'] = 'True'

import numpy as np
from algorithms.environments.single_agent_env.custom_parking import CustomParkingEnv
from argparse import Namespace

def test_parking_behavior():
    """测试停车行为"""
    print("🚗 测试强化停车行为")
    print("=" * 50)
    print("新改进内容:")
    print("1. 强化停车奖励：分层距离奖励机制")
    print("2. 停车动作识别：明确奖励停车意图")
    print("3. 连续停车奖励：鼓励保持停车状态")
    print("4. 朝向奖励：鼓励正确停车朝向")
    print("5. 延迟终止：给智能体调整时间")
    print("-" * 50)
    
    # 创建环境
    config = Namespace()
    config.env_seed = 42
    config.max_episode_steps = 80
    config.collision_reward = -1.0
    config.render_mode = None
    config.debug = True  # 开启调试
    
    env = CustomParkingEnv(config)
    
    # 测试几个回合
    num_episodes = 3
    
    for episode in range(num_episodes):
        print(f"\n📊 测试回合 {episode + 1}/{num_episodes}")
        
        obs, info = env.reset()
        done = False
        step_count = 0
        episode_reward = 0
        
        # 记录关键状态
        min_distance = float('inf')
        max_speed = 0
        final_approach_steps = 0
        
        while not done and step_count < 80:
            # 获取车辆状态
            vehicle = env.controlled_vehicles[0]
            goal_distance = np.linalg.norm(vehicle.position - vehicle.goal.position)
            current_speed = abs(vehicle.speed)
            
            min_distance = min(min_distance, goal_distance)
            max_speed = max(max_speed, current_speed)
            
            # 记录接近目标的步数
            if goal_distance < 3.0:
                final_approach_steps += 1
            
            # 改进的智能体策略：测试强化停车行为
            if goal_distance > 8.0:
                action = np.array([0.8, 0.0])  # 远距离：加速
            elif goal_distance > 5.0:
                action = np.array([0.5, 0.0])  # 中距离：适度
            elif goal_distance > 3.0:
                # 准备停车阶段：开始减速
                if current_speed > 2.0:
                    action = np.array([-0.3, 0.0])  # 减速
                else:
                    action = np.array([0.2, 0.0])   # 慢速接近
            elif goal_distance > 1.5:
                # 接近目标：强制减速
                if current_speed > 1.0:
                    action = np.array([-0.4, 0.0])  # 强制减速
                else:
                    action = np.array([0.1, 0.0])   # 极慢接近
            else:
                # 停车区域：明确停车动作
                if current_speed > 0.5:
                    action = np.array([-0.5, 0.0])  # 强制刹车
                elif current_speed > 0.2:
                    action = np.array([-0.2, 0.0])  # 轻微刹车
                else:
                    action = np.array([0.0, 0.0])   # 保持停车
            
            # 执行动作
            obs, reward, terminated, truncated, info = env.step(action)
            done = terminated or truncated
            
            episode_reward += reward
            step_count += 1
            
            # 每15步打印状态，增加停车奖励信息
            if step_count % 15 == 0:
                # 获取奖励分解信息
                reward_info = ""
                if 'reward_breakdown' in info:
                    breakdown = info['reward_breakdown']
                    reward_info = f", 停车奖励={breakdown.get('parking_reward', 0):.3f}"

                print(f"  步骤 {step_count}: 距离={goal_distance:.2f}m, 速度={current_speed:.2f}m/s, "
                      f"奖励={reward:.3f}{reward_info}")

                # 在停车区域内时，显示详细信息
                if goal_distance < 2.0:
                    print(f"    🎯 停车区域: 加速度={action[0]:.2f}, 连续停车步数={getattr(env, 'consecutive_parking_steps', 0)}")
        
        # 最终分析
        final_distance = np.linalg.norm(vehicle.position - vehicle.goal.position)
        final_speed = abs(vehicle.speed)
        
        print(f"\n  回合结束分析:")
        print(f"    最终距离: {final_distance:.2f}m")
        print(f"    最终速度: {final_speed:.2f}m/s")
        print(f"    最近距离: {min_distance:.2f}m")
        print(f"    最大速度: {max_speed:.2f}m/s")
        print(f"    接近目标步数: {final_approach_steps}")
        print(f"    总步数: {step_count}")
        print(f"    总奖励: {episode_reward:.2f}")
        
        # 检查终止原因
        if 'termination_reason' in info:
            print(f"    终止原因: {info['termination_reason']}")
        
        # 分析停车行为
        if final_distance < 2.0:
            if final_speed < 1.0:
                print(f"  🎯 停车行为: 优秀 (接近目标且低速)")
            elif final_speed < 2.0:
                print(f"  🎯 停车行为: 良好 (接近目标但速度稍高)")
            else:
                print(f"  ⚠️ 停车行为: 需改进 (接近目标但速度过高)")
        else:
            print(f"  ❌ 停车行为: 失败 (未能接近目标)")
        
        # 分析延迟终止效果
        if final_approach_steps > 10:
            print(f"  ⏰ 延迟终止: 生效 (在目标附近停留{final_approach_steps}步)")
        else:
            print(f"  ⏰ 延迟终止: 未充分利用 (在目标附近仅{final_approach_steps}步)")
    
    env.close()
    
    print(f"\n💡 改进效果总结:")
    print("1. 如果看到'检测到终止条件'和'延迟中...'信息，说明延迟终止生效")
    print("2. 如果最终速度较低，说明停车奖励起作用")
    print("3. 如果接近目标步数较多，说明智能体学会了在目标附近调整")
    print("4. 现在重新训练应该能学到更好的停车行为")

def main():
    """主函数"""
    print("🚀 停车行为测试")
    print("=" * 50)
    
    test_parking_behavior()
    
    print("\n✅ 测试完成！")
    print("\n🎯 关键改进:")
    print("1. 🛑 停车奖励：鼓励在目标附近低速行驶")
    print("2. ⚠️ 高速惩罚：避免高速冲过目标")
    print("3. ⏰ 延迟终止：给智能体调整时间")
    print("4. 🔍 调试信息：帮助理解行为")

if __name__ == "__main__":
    main()
