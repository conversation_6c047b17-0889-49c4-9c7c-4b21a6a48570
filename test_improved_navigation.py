#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试改进的导航机制：简化路径规划 + 朝向目标奖励
"""

import warnings
import os
warnings.filterwarnings('ignore')
os.environ['GYMNASIUM_DISABLE_WARNINGS'] = 'True'

import numpy as np
from algorithms.environments.single_agent_env.custom_parking import CustomParkingEnv
from argparse import Namespace

def test_improved_navigation():
    """测试改进的导航机制"""
    print("🚗 测试改进的导航机制")
    print("=" * 60)
    print("改进内容:")
    print("1. 简化路径规划：只提供关键拐点，不考虑车头朝向")
    print("2. 朝向目标奖励：主要鼓励朝向目标前进")
    print("3. 减少路径依赖：路径只作参考，不强制跟随")
    print("4. 防止倒车：明确惩罚倒车行为")
    print("5. 鼓励自主转弯：让智能体学会自己决定如何到达")
    print("-" * 60)
    
    # 创建环境
    config = Namespace()
    config.env_seed = 42
    config.max_episode_steps = 120
    config.collision_reward = -1.0
    config.render_mode = None
    config.debug = True
    
    env = CustomParkingEnv(config)
    
    # 测试不同的导航场景
    test_scenarios = [
        {
            "name": "正向接近测试",
            "description": "车头朝向目标方向",
            "action_strategy": "forward_approach"
        },
        {
            "name": "侧向接近测试", 
            "description": "车头垂直于目标方向",
            "action_strategy": "side_approach"
        },
        {
            "name": "反向接近测试",
            "description": "车头背离目标方向",
            "action_strategy": "reverse_approach"
        },
        {
            "name": "自由导航测试",
            "description": "让智能体自由选择路径",
            "action_strategy": "free_navigation"
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\n📊 {scenario['name']}")
        print(f"描述: {scenario['description']}")
        print("-" * 40)
        
        obs, info = env.reset()
        done = False
        step_count = 0
        episode_reward = 0
        
        # 记录导航行为
        forward_steps = 0
        reverse_steps = 0
        turning_steps = 0
        goal_direction_rewards = []
        
        while not done and step_count < 120:
            vehicle = env.controlled_vehicles[0]
            goal_distance = np.linalg.norm(vehicle.position - vehicle.goal.position)
            current_speed = vehicle.speed
            
            # 计算朝向目标的角度差
            goal_direction = np.arctan2(
                vehicle.goal.position[1] - vehicle.position[1],
                vehicle.goal.position[0] - vehicle.position[0]
            )
            heading_diff = abs(vehicle.heading - goal_direction)
            heading_diff = min(heading_diff, 2*np.pi - heading_diff)
            
            # 根据测试策略选择动作
            action = get_navigation_action(scenario['action_strategy'], 
                                         goal_distance, current_speed, 
                                         heading_diff, step_count)
            
            # 执行动作
            obs, reward, terminated, truncated, info = env.step(action)
            done = terminated or truncated
            
            episode_reward += reward
            step_count += 1
            
            # 记录行为统计
            if current_speed > 0.2:
                forward_steps += 1
            elif current_speed < -0.2:
                reverse_steps += 1
            
            if abs(action[1]) > 0.1:  # 转向动作
                turning_steps += 1
            
            # 记录朝向目标奖励
            if 'reward_breakdown' in info:
                breakdown = info['reward_breakdown']
                if 'path_reward' in breakdown:
                    goal_direction_rewards.append(breakdown['path_reward'])
            
            # 显示关键信息
            if step_count % 20 == 0:
                print(f"  步骤 {step_count}: 距离={goal_distance:.2f}m, 速度={current_speed:.2f}m/s, "
                      f"朝向差={np.degrees(heading_diff):.1f}°")
                
                if 'reward_breakdown' in info:
                    breakdown = info['reward_breakdown']
                    print(f"    奖励分解: 导航={breakdown.get('path_reward', 0):.3f}, "
                          f"停车={breakdown.get('parking_reward', 0):.3f}")
        
        # 分析导航行为
        final_distance = np.linalg.norm(vehicle.position - vehicle.goal.position)
        final_speed = vehicle.speed
        avg_goal_reward = np.mean(goal_direction_rewards) if goal_direction_rewards else 0
        
        print(f"\n  导航行为分析:")
        print(f"    总步数: {step_count}")
        print(f"    前进步数: {forward_steps} ({forward_steps/step_count*100:.1f}%)")
        print(f"    倒车步数: {reverse_steps} ({reverse_steps/step_count*100:.1f}%)")
        print(f"    转弯步数: {turning_steps} ({turning_steps/step_count*100:.1f}%)")
        print(f"    平均导航奖励: {avg_goal_reward:.3f}")
        print(f"    最终距离: {final_distance:.2f}m")
        print(f"    最终速度: {final_speed:.2f}m/s")
        print(f"    总奖励: {episode_reward:.2f}")
        
        # 评估导航效果
        evaluate_navigation_result(scenario, forward_steps, reverse_steps, 
                                 final_distance, avg_goal_reward)
    
    env.close()
    
    print(f"\n💡 改进导航机制总结:")
    print("✅ 优点:")
    print("  1. 减少倒车：通过朝向目标奖励鼓励前进")
    print("  2. 自主转弯：智能体学会自己决定转弯时机")
    print("  3. 简化路径：不强制跟随复杂路径")
    print("  4. 自然导航：更符合人类驾驶习惯")

def get_navigation_action(strategy, goal_distance, current_speed, heading_diff, step_count):
    """根据导航策略生成动作"""
    if strategy == "forward_approach":
        # 正向接近：主要前进，适当转向
        if goal_distance > 3.0:
            if heading_diff > np.pi/4:  # 45度以上需要转向
                return np.array([0.3, 0.3 if heading_diff > 0 else -0.3])
            else:
                return np.array([0.6, 0.0])  # 直行
        else:
            return np.array([0.2, 0.0])  # 慢速接近
            
    elif strategy == "side_approach":
        # 侧向接近：需要大幅转向
        if goal_distance > 5.0:
            return np.array([0.4, 0.4])  # 转向同时前进
        elif goal_distance > 2.0:
            return np.array([0.3, 0.2])  # 减速转向
        else:
            return np.array([0.1, 0.0])  # 慢速接近
            
    elif strategy == "reverse_approach":
        # 反向接近：测试是否会倒车
        if goal_distance > 8.0:
            # 远距离时，看智能体是否选择倒车（不应该）
            return np.array([0.5, 0.0])  # 尝试前进
        elif goal_distance > 4.0:
            # 中距离时，需要转向
            return np.array([0.3, 0.5])  # 大幅转向
        else:
            return np.array([0.2, 0.0])  # 接近目标
            
    elif strategy == "free_navigation":
        # 自由导航：基于距离的简单策略
        if goal_distance > 6.0:
            return np.array([0.7, 0.0])  # 加速接近
        elif goal_distance > 3.0:
            return np.array([0.4, 0.1])  # 适度前进，轻微转向
        elif goal_distance > 1.0:
            return np.array([0.2, 0.0])  # 慢速接近
        else:
            return np.array([0.0, 0.0])  # 尝试停车
    
    return np.array([0.0, 0.0])

def evaluate_navigation_result(scenario, forward_steps, reverse_steps, final_distance, avg_goal_reward):
    """评估导航结果"""
    strategy = scenario['action_strategy']
    
    # 检查是否有过多倒车
    if reverse_steps > forward_steps * 0.3:  # 倒车超过30%
        print(f"    ⚠️ 警告: 倒车过多 ({reverse_steps} 步)")
    else:
        print(f"    ✅ 倒车控制: 良好 ({reverse_steps} 步)")
    
    # 检查导航效果
    if final_distance < 2.0:
        print(f"    ✅ 导航效果: 优秀 (到达目标)")
    elif final_distance < 5.0:
        print(f"    ⭐ 导航效果: 良好 (接近目标)")
    else:
        print(f"    ❌ 导航效果: 需改进 (距离目标较远)")
    
    # 检查奖励机制
    if avg_goal_reward > 0.1:
        print(f"    ✅ 奖励机制: 有效 (平均{avg_goal_reward:.3f})")
    elif avg_goal_reward > 0:
        print(f"    ⭐ 奖励机制: 一般 (平均{avg_goal_reward:.3f})")
    else:
        print(f"    ❌ 奖励机制: 需调整 (平均{avg_goal_reward:.3f})")

def main():
    """主函数"""
    print("🚀 改进导航机制测试")
    print("=" * 60)
    
    test_improved_navigation()
    
    print("\n✅ 测试完成！")
    print("\n🎯 关键改进:")
    print("1. 🎯 朝向目标：主要奖励朝向目标前进")
    print("2. 🚫 防止倒车：明确惩罚倒车行为")
    print("3. 🛣️ 简化路径：只提供关键点作参考")
    print("4. 🔄 自主转弯：让智能体学会自己转弯")
    print("5. 🎮 自然导航：更符合直觉的导航方式")

if __name__ == "__main__":
    main()
