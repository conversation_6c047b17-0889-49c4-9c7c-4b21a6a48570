#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试失败原因分析
"""

import warnings
import os
warnings.filterwarnings('ignore')
os.environ['GYMNASIUM_DISABLE_WARNINGS'] = 'True'

import numpy as np
from algorithms.environments.single_agent_env.custom_parking import CustomParkingEnv
from argparse import Namespace

def test_failure_reasons():
    """测试并分析失败原因"""
    print("🔍 失败原因分析测试")
    print("=" * 50)
    
    # 创建环境
    config = Namespace()
    config.env_seed = 42
    config.max_episode_steps = 60  # 和测试脚本一样的步数
    config.collision_reward = -1.0
    config.render_mode = None
    config.debug = True  # 开启调试，看终止原因
    
    env = CustomParkingEnv(config)
    
    # 测试几个回合
    num_episodes = 5
    failure_reasons = []
    
    for episode in range(num_episodes):
        print(f"\n📊 测试回合 {episode + 1}/{num_episodes}")
        
        obs, info = env.reset()
        done = False
        step_count = 0
        episode_reward = 0
        
        while not done and step_count < 60:
            # 获取车辆状态
            vehicle = env.controlled_vehicles[0]
            goal_distance = np.linalg.norm(vehicle.position - vehicle.goal.position)
            current_speed = abs(vehicle.speed)
            
            # 简单的测试策略（模拟智能体行为）
            if goal_distance > 8.0:
                action = np.array([0.8, 0.0])  # 较大加速
            elif goal_distance > 4.0:
                action = np.array([0.5, 0.0])  # 中等加速
            elif goal_distance > 2.0:
                action = np.array([0.3, 0.0])  # 小幅加速
            else:
                if current_speed > 2.0:
                    action = np.array([-0.3, 0.0])  # 减速
                else:
                    action = np.array([0.1, 0.0])   # 微调
            
            # 执行动作
            obs, reward, terminated, truncated, info = env.step(action)
            done = terminated or truncated
            
            episode_reward += reward
            step_count += 1
            
            # 每20步打印一次状态
            if step_count % 20 == 0:
                print(f"  步骤 {step_count}: 距离={goal_distance:.2f}m, 速度={current_speed:.2f}m/s, "
                      f"碰撞={vehicle.crashed}")
        
        # 分析终止原因
        final_distance = np.linalg.norm(vehicle.position - vehicle.goal.position)
        final_speed = abs(vehicle.speed)
        
        if terminated:
            termination_reason = info.get('termination_reason', '未知')
            print(f"  ✅ 正常终止: {termination_reason}")
            result_type = "正常终止"
        elif truncated:
            print(f"  ⏰ 超时终止: 达到最大步数 {step_count}")
            result_type = "超时"
            termination_reason = f"超时 (最大步数={step_count})"
        else:
            print(f"  ❓ 其他终止")
            result_type = "其他"
            termination_reason = "其他原因"
        
        print(f"  最终状态:")
        print(f"    距离: {final_distance:.2f}m")
        print(f"    速度: {final_speed:.2f}m/s")
        print(f"    碰撞: {vehicle.crashed}")
        print(f"    总步数: {step_count}")
        print(f"    总奖励: {episode_reward:.2f}")
        
        # 判断成功/失败
        if final_distance < 2.0 and not vehicle.crashed:
            success = True
            print(f"  🎯 判定: 成功")
        else:
            success = False
            print(f"  ❌ 判定: 失败")
            
            # 分析失败原因
            if vehicle.crashed:
                failure_reason = "碰撞"
            elif final_distance >= 2.0:
                failure_reason = f"距离过远 ({final_distance:.2f}m)"
            elif step_count >= 60:
                failure_reason = "超时"
            else:
                failure_reason = "其他"
            
            failure_reasons.append(failure_reason)
            print(f"  🔍 失败原因: {failure_reason}")
    
    env.close()
    
    # 统计失败原因
    print(f"\n📈 失败原因统计:")
    print("-" * 30)
    
    if failure_reasons:
        from collections import Counter
        reason_counts = Counter(failure_reasons)
        
        for reason, count in reason_counts.most_common():
            percentage = count / len(failure_reasons) * 100
            print(f"{reason}: {count}次 ({percentage:.1f}%)")
    else:
        print("没有失败案例！")
    
    # 给出改进建议
    print(f"\n💡 改进建议:")
    if failure_reasons:
        most_common_reason = Counter(failure_reasons).most_common(1)[0][0]
        
        if "碰撞" in most_common_reason:
            print("- 主要问题是碰撞，建议:")
            print("  * 降低最大速度限制")
            print("  * 增加碰撞惩罚")
            print("  * 改进路径规划避开障碍物")
        elif "距离过远" in most_common_reason:
            print("- 主要问题是无法接近目标，建议:")
            print("  * 检查路径规划是否正确")
            print("  * 增加接近目标的奖励")
            print("  * 检查是否有局部最优问题")
        elif "超时" in most_common_reason:
            print("- 主要问题是超时，建议:")
            print("  * 增加最大步数限制")
            print("  * 提高智能体的学习效率")
            print("  * 简化任务难度")
    else:
        print("- 所有测试都成功，表现良好！")
    
    return failure_reasons

def main():
    """主函数"""
    print("🚀 失败原因分析")
    print("=" * 50)
    
    # 分析失败原因
    failure_reasons = test_failure_reasons()
    
    print("\n✅ 分析完成！")
    print("\n🎯 现在你可以:")
    print("1. 查看上面的失败原因统计")
    print("2. 根据主要失败原因进行针对性改进")
    print("3. 重新运行测试验证改进效果")

if __name__ == "__main__":
    main()
