#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试强化停车学习机制：验证非强制的停车教学方法
"""

import warnings
import os
warnings.filterwarnings('ignore')
os.environ['GYMNASIUM_DISABLE_WARNINGS'] = 'True'

import numpy as np
from algorithms.environments.single_agent_env.custom_parking import CustomParkingEnv
from argparse import Namespace

def test_enhanced_parking_learning():
    """测试强化停车学习机制"""
    print("🚗 测试强化停车学习机制")
    print("=" * 60)
    print("非强制停车教学方法:")
    print("1. 渐进式停车奖励：距离越近对速度要求越严格")
    print("2. 停车动作识别：奖励正确的停车行为")
    print("3. 停车状态维持：鼓励保持停车状态")
    print("4. 停车示范学习：通过奖励引导而非强制")
    print("5. 课程学习：从简单到复杂的停车场景")
    print("-" * 60)
    
    # 创建环境
    config = Namespace()
    config.env_seed = 42
    config.max_episode_steps = 100
    config.collision_reward = -1.0
    config.render_mode = None
    config.debug = True
    
    env = CustomParkingEnv(config)
    
    # 测试不同的停车场景
    scenarios = [
        {"name": "远距离接近", "test_distance": 8.0},
        {"name": "中距离减速", "test_distance": 4.0},
        {"name": "近距离停车", "test_distance": 2.0},
        {"name": "精确停车", "test_distance": 1.0}
    ]
    
    for scenario in scenarios:
        print(f"\n📊 测试场景: {scenario['name']}")
        print("-" * 40)
        
        obs, info = env.reset()
        vehicle = env.controlled_vehicles[0]
        
        # 模拟不同距离的停车行为
        test_actions = []
        rewards = []
        
        for step in range(20):  # 测试20步
            goal_distance = np.linalg.norm(vehicle.position - vehicle.goal.position)
            current_speed = abs(vehicle.speed)
            
            # 根据场景选择测试动作
            if scenario["name"] == "远距离接近":
                action = np.array([0.6, 0.0])  # 正常加速
            elif scenario["name"] == "中距离减速":
                action = np.array([0.2, 0.0]) if current_speed < 2.0 else np.array([-0.3, 0.0])
            elif scenario["name"] == "近距离停车":
                if current_speed > 1.0:
                    action = np.array([-0.4, 0.0])  # 减速
                else:
                    action = np.array([0.1, 0.0])   # 慢速接近
            else:  # 精确停车
                if current_speed > 0.5:
                    action = np.array([-0.5, 0.0])  # 强制减速
                elif current_speed > 0.2:
                    action = np.array([-0.2, 0.0])  # 轻微减速
                else:
                    action = np.array([0.0, 0.0])   # 保持停车
            
            obs, reward, terminated, truncated, info = env.step(action)
            
            test_actions.append(action.copy())
            rewards.append(reward)
            
            # 检查停车示范
            if env._should_demonstrate_parking():
                demo_action = env._get_parking_demonstration_action()
                if demo_action is not None:
                    similarity = env._compute_action_similarity(action, demo_action)
                    print(f"  步骤 {step+1}: 示范学习 - 相似度={similarity:.2f}")
            
            # 显示关键信息
            if step % 5 == 0:
                parking_reward = 0
                if 'reward_breakdown' in info:
                    parking_reward = info['reward_breakdown'].get('parking_reward', 0)
                
                print(f"  步骤 {step+1}: 距离={goal_distance:.2f}m, 速度={current_speed:.2f}m/s, "
                      f"停车奖励={parking_reward:.3f}")
                
                # 显示停车状态
                if hasattr(env, 'parking_state_steps') and env.parking_state_steps > 0:
                    print(f"    🎯 连续停车状态: {env.parking_state_steps} 步")
            
            if terminated or truncated:
                break
        
        # 分析场景结果
        avg_reward = np.mean(rewards)
        final_distance = np.linalg.norm(vehicle.position - vehicle.goal.position)
        final_speed = abs(vehicle.speed)
        
        print(f"\n  场景分析:")
        print(f"    平均奖励: {avg_reward:.3f}")
        print(f"    最终距离: {final_distance:.2f}m")
        print(f"    最终速度: {final_speed:.2f}m/s")
        
        # 评估停车学习效果
        if final_distance < 1.5 and final_speed < 0.5:
            print(f"    🎯 停车学习: 优秀 ✅")
        elif final_distance < 2.0 and final_speed < 1.0:
            print(f"    🎯 停车学习: 良好 ⭐")
        elif final_distance < 3.0:
            print(f"    🎯 停车学习: 需改进 ⚠️")
        else:
            print(f"    🎯 停车学习: 失败 ❌")
    
    env.close()
    
    print(f"\n💡 强化停车学习总结:")
    print("✅ 优点:")
    print("  1. 非强制性：通过奖励引导而非强制停车")
    print("  2. 渐进式：从远距离到近距离逐步教学")
    print("  3. 示范学习：提供正确动作的奖励引导")
    print("  4. 状态维持：鼓励保持正确的停车状态")
    print("\n🔧 建议:")
    print("  1. 增加训练时间让智能体充分学习停车概念")
    print("  2. 调整奖励权重平衡路径跟踪和停车行为")
    print("  3. 考虑使用课程学习从简单场景开始训练")

def main():
    """主函数"""
    print("🚀 强化停车学习测试")
    print("=" * 60)
    
    test_enhanced_parking_learning()
    
    print("\n✅ 测试完成！")
    print("\n🎯 关键改进:")
    print("1. 🎓 渐进式教学：距离分层的停车要求")
    print("2. 🎯 动作识别：明确奖励停车行为")
    print("3. 🔄 状态维持：鼓励保持停车状态")
    print("4. 👨‍🏫 示范学习：非强制的动作引导")
    print("5. 📚 课程学习：从简单到复杂的学习过程")

if __name__ == "__main__":
    main()
